-- Email Monitor Agent - PostgreSQL Initialization Script
-- This script sets up the database with proper permissions and extensions

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create indexes for better performance
-- Note: Tables will be created by SQLAlchemy, but we can add indexes here

-- Function to create indexes after tables are created
CREATE OR REPLACE FUNCTION create_email_monitor_indexes()
RETURNS void AS $$
BEGIN
    -- Check if tables exist before creating indexes
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'email_logs') THEN
        -- Indexes for email_logs table
        CREATE INDEX IF NOT EXISTS idx_email_logs_message_id ON email_logs(message_id);
        CREATE INDEX IF NOT EXISTS idx_email_logs_sender ON email_logs(sender);
        CREATE INDEX IF NOT EXISTS idx_email_logs_status ON email_logs(status);
        CREATE INDEX IF NOT EXISTS idx_email_logs_received_at ON email_logs(received_at DESC);
        CREATE INDEX IF NOT EXISTS idx_email_logs_processed_at ON email_logs(processed_at DESC);
        
        -- Full-text search index for email content
        CREATE INDEX IF NOT EXISTS idx_email_logs_subject_gin ON email_logs USING gin(to_tsvector('english', subject));
        CREATE INDEX IF NOT EXISTS idx_email_logs_summary_gin ON email_logs USING gin(to_tsvector('english', summary));
    END IF;

    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'whatsapp_notifications') THEN
        -- Indexes for whatsapp_notifications table
        CREATE INDEX IF NOT EXISTS idx_whatsapp_notifications_email_log_id ON whatsapp_notifications(email_log_id);
        CREATE INDEX IF NOT EXISTS idx_whatsapp_notifications_recipient ON whatsapp_notifications(recipient);
        CREATE INDEX IF NOT EXISTS idx_whatsapp_notifications_status ON whatsapp_notifications(status);
        CREATE INDEX IF NOT EXISTS idx_whatsapp_notifications_sent_at ON whatsapp_notifications(sent_at DESC);
        CREATE INDEX IF NOT EXISTS idx_whatsapp_notifications_retry_count ON whatsapp_notifications(retry_count);
    END IF;

    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'email_replies') THEN
        -- Indexes for email_replies table
        CREATE INDEX IF NOT EXISTS idx_email_replies_email_log_id ON email_replies(email_log_id);
        CREATE INDEX IF NOT EXISTS idx_email_replies_reply_to ON email_replies(reply_to);
        CREATE INDEX IF NOT EXISTS idx_email_replies_status ON email_replies(status);
        CREATE INDEX IF NOT EXISTS idx_email_replies_sent_at ON email_replies(sent_at DESC);
    END IF;

    RAISE NOTICE 'Email Monitor indexes created successfully';
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO email_agent;
GRANT CREATE ON SCHEMA public TO email_agent;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO email_agent;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO email_agent;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO email_agent;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO email_agent;

-- Create a function to get database statistics
CREATE OR REPLACE FUNCTION get_email_monitor_stats()
RETURNS TABLE(
    table_name text,
    row_count bigint,
    table_size text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.table_name::text,
        COALESCE(s.n_tup_ins + s.n_tup_upd - s.n_tup_del, 0) as row_count,
        pg_size_pretty(pg_total_relation_size(quote_ident(t.table_name)::regclass)) as table_size
    FROM information_schema.tables t
    LEFT JOIN pg_stat_user_tables s ON s.relname = t.table_name
    WHERE t.table_schema = 'public' 
    AND t.table_name IN ('email_logs', 'whatsapp_notifications', 'email_replies')
    ORDER BY t.table_name;
END;
$$ LANGUAGE plpgsql;

-- Create a function to clean old data (optional maintenance)
CREATE OR REPLACE FUNCTION cleanup_old_email_data(days_to_keep integer DEFAULT 90)
RETURNS integer AS $$
DECLARE
    deleted_count integer := 0;
    cutoff_date timestamp;
BEGIN
    cutoff_date := NOW() - (days_to_keep || ' days')::interval;
    
    -- Delete old email logs and related data
    WITH deleted_emails AS (
        DELETE FROM email_logs 
        WHERE received_at < cutoff_date 
        AND status IN ('processed', 'failed')
        RETURNING id
    )
    SELECT COUNT(*) INTO deleted_count FROM deleted_emails;
    
    -- Note: Related notifications and replies will be deleted by CASCADE
    
    RAISE NOTICE 'Cleaned up % old email records older than % days', deleted_count, days_to_keep;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Log initialization
DO $$
BEGIN
    RAISE NOTICE 'Email Monitor PostgreSQL initialization completed';
    RAISE NOTICE 'Database: %', current_database();
    RAISE NOTICE 'User: %', current_user;
    RAISE NOTICE 'Timestamp: %', NOW();
END $$;
