import imaplib
import email
import email.header
import logging
import re
from datetime import datetime, timezone
from email.utils import parseaddr
from typing import Dict, List, Optional

from ..models import EmailLog

logger = logging.getLogger(__name__)

class EmailMonitor:
    """
    Class for monitoring email inbox using IMAP protocol.
    Handles connection, authentication, email fetching, and parsing.
    """

    def __init__(self,
                 host: str,
                 username: str,
                 password: str,
                 port: int = 993,
                 use_ssl: bool = True,
                 mailbox: str = 'INBOX'):
        """
        Initialize the email monitor with connection parameters.

        Args:
            host: IMAP server hostname
            username: Email account username
            password: Email account password
            port: IMAP server port (default: 993)
            use_ssl: Whether to use SSL for connection (default: True)
            mailbox: Mailbox to monitor (default: 'INBOX')
        """
        self.host = host
        self.username = username
        self.password = password
        self.port = port
        self.use_ssl = use_ssl
        self.mailbox = mailbox
        self.connection = None

    def connect(self) -> bool:
        """
        Establish connection to the IMAP server.

        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            if self.use_ssl:
                self.connection = imaplib.IMAP4_SSL(self.host, self.port)
            else:
                self.connection = imaplib.IMAP4(self.host, self.port)

            self.connection.login(self.username, self.password)
            logger.info(f"Successfully connected to {self.host} as {self.username}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to {self.host}: {str(e)}")
            return False

    def disconnect(self) -> None:
        """
        Close the connection to the IMAP server.
        """
        if self.connection:
            try:
                self.connection.logout()
                logger.info("Disconnected from IMAP server")
            except Exception as e:
                logger.error(f"Error during disconnect: {str(e)}")
            finally:
                self.connection = None

    def select_mailbox(self, mailbox: Optional[str] = None) -> bool:
        """
        Select a mailbox to monitor.

        Args:
            mailbox: Mailbox name (default: None, uses instance mailbox)

        Returns:
            bool: True if mailbox selection successful, False otherwise
        """
        if not self.connection:
            logger.error("Not connected to IMAP server")
            return False

        mailbox_name = mailbox or self.mailbox

        try:
            status, data = self.connection.select(mailbox_name)
            if status == 'OK':
                logger.info(f"Selected mailbox: {mailbox_name}")
                return True
            else:
                logger.error(f"Failed to select mailbox {mailbox_name}: {data}")
                return False
        except Exception as e:
            logger.error(f"Error selecting mailbox {mailbox_name}: {str(e)}")
            return False

    def search_emails(self, criteria: str = 'UNSEEN') -> List[str]:
        """
        Search for emails matching the given criteria.

        Args:
            criteria: IMAP search criteria (default: 'UNSEEN')

        Returns:
            List[str]: List of email IDs matching the criteria
        """
        if not self.connection:
            logger.error("Not connected to IMAP server")
            return []

        try:
            status, data = self.connection.search(None, criteria)
            if status == 'OK':
                # Convert byte string to list of IDs
                email_ids = data[0].split()
                return [eid.decode() for eid in email_ids]
            else:
                logger.error(f"Search failed: {data}")
                return []
        except Exception as e:
            logger.error(f"Error searching emails: {str(e)}")
            return []

    def fetch_email(self, email_id: str) -> Optional[Dict]:
        """
        Fetch and parse an email by ID.

        Args:
            email_id: Email ID to fetch

        Returns:
            Optional[Dict]: Parsed email data or None if fetch failed
        """
        if not self.connection:
            logger.error("Not connected to IMAP server")
            return None

        try:
            status, data = self.connection.fetch(email_id, '(RFC822)')
            if status != 'OK':
                logger.error(f"Failed to fetch email {email_id}: {data}")
                return None

            raw_email = data[0][1]
            email_message = email.message_from_bytes(raw_email)

            # Parse email data
            parsed_email = self._parse_email(email_message)
            parsed_email['email_id'] = email_id

            return parsed_email
        except Exception as e:
            logger.error(f"Error fetching email {email_id}: {str(e)}")
            return None

    def mark_as_read(self, email_id: str) -> bool:
        """
        Mark an email as read.

        Args:
            email_id: Email ID to mark as read

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.connection:
            logger.error("Not connected to IMAP server")
            return False

        try:
            status, data = self.connection.store(email_id, '+FLAGS', '\\Seen')
            if status == 'OK':
                logger.info(f"Marked email {email_id} as read")
                return True
            else:
                logger.error(f"Failed to mark email {email_id} as read: {data}")
                return False
        except Exception as e:
            logger.error(f"Error marking email {email_id} as read: {str(e)}")
            return False

    def _parse_email(self, email_message) -> Dict:
        """
        Parse an email message into a structured dictionary.

        Args:
            email_message: Email message object

        Returns:
            Dict: Structured email data
        """
        # Extract header information
        subject = self._decode_header(email_message.get('Subject', ''))
        from_header = email_message.get('From', '')
        sender_name, sender_email = parseaddr(from_header)
        sender_name = self._decode_header(sender_name)

        to_header = email_message.get('To', '')
        recipient_name, recipient_email = parseaddr(to_header)
        recipient_name = self._decode_header(recipient_name)

        date_str = email_message.get('Date', '')
        message_id = email_message.get('Message-ID', '')

        # Extract body content
        body_text = ""
        body_html = ""
        attachments = []

        if email_message.is_multipart():
            for part in email_message.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))

                # Skip multipart containers
                if content_type == "multipart/alternative":
                    continue

                # Handle attachments
                if "attachment" in content_disposition:
                    filename = part.get_filename()
                    if filename:
                        # Decode filename if needed
                        filename = self._decode_header(filename)
                        attachments.append({
                            'filename': filename,
                            'content_type': content_type,
                            'size': len(part.get_payload(decode=True))
                        })
                    continue

                # Get the email body
                try:
                    body = part.get_payload(decode=True)
                    if body:
                        charset = part.get_content_charset() or 'utf-8'
                        try:
                            decoded_body = body.decode(charset)
                        except UnicodeDecodeError:
                            decoded_body = body.decode('latin-1')

                        if content_type == "text/plain":
                            body_text = decoded_body
                        elif content_type == "text/html":
                            body_html = decoded_body
                except Exception as e:
                    logger.error(f"Error decoding email body: {str(e)}")
        else:
            # Not multipart - get the content directly
            content_type = email_message.get_content_type()
            try:
                body = email_message.get_payload(decode=True)
                if body:
                    charset = email_message.get_content_charset() or 'utf-8'
                    try:
                        decoded_body = body.decode(charset)
                    except UnicodeDecodeError:
                        decoded_body = body.decode('latin-1')

                    if content_type == "text/plain":
                        body_text = decoded_body
                    elif content_type == "text/html":
                        body_html = decoded_body
            except Exception as e:
                logger.error(f"Error decoding email body: {str(e)}")

        # Construct the result
        return {
            'message_id': message_id,
            'subject': subject,
            'sender': {
                'name': sender_name,
                'email': sender_email
            },
            'recipient': {
                'name': recipient_name,
                'email': recipient_email
            },
            'date': date_str,
            'body': {
                'text': body_text,
                'html': body_html
            },
            'attachments': attachments
        }

    def _decode_header(self, header: str) -> str:
        """
        Decode email header.

        Args:
            header: Email header string

        Returns:
            str: Decoded header string
        """
        if not header:
            return ""

        try:
            decoded_header = email.header.decode_header(header)
            header_parts = []

            for part, encoding in decoded_header:
                if isinstance(part, bytes):
                    if encoding:
                        try:
                            decoded_part = part.decode(encoding)
                        except (UnicodeDecodeError, LookupError):
                            decoded_part = part.decode('utf-8', errors='replace')
                    else:
                        decoded_part = part.decode('utf-8', errors='replace')
                else:
                    decoded_part = part

                header_parts.append(decoded_part)

            return ' '.join(header_parts)
        except Exception as e:
            logger.error(f"Error decoding header: {str(e)}")
            return header


class EmailProcessor:
    """
    Class for processing emails, storing them in the database,
    and preparing them for AI summarization and notification.
    """

    def __init__(self, db_session, allowed_sender_email: Optional[str] = None):
        """
        Initialize the email processor.

        Args:
            db_session: SQLAlchemy database session
            allowed_sender_email: Optional email address to filter by. If provided, only emails from this sender will be processed.
        """
        self.db_session = db_session
        self.allowed_sender_email = allowed_sender_email.lower().strip() if allowed_sender_email else None

    def process_email(self, email_data: Dict) -> Optional[EmailLog]:
        """
        Process an email and store it in the database.

        Args:
            email_data: Parsed email data

        Returns:
            Optional[EmailLog]: Created EmailLog instance or None if processing failed
        """
        try:
            # Check sender filtering if configured
            if self.allowed_sender_email:
                sender_email = email_data['sender']['email'].lower().strip()
                if sender_email != self.allowed_sender_email:
                    logger.info(f"Skipping email from {sender_email} - not from allowed sender {self.allowed_sender_email}")
                    return None

            # Check if email already exists in database
            existing_email = self.db_session.query(EmailLog).filter_by(
                message_id=email_data['message_id']
            ).first()

            if existing_email:
                logger.info(f"Email with message ID {email_data['message_id']} already processed")
                return existing_email

            # Create new email log entry
            email_log = EmailLog(
                message_id=email_data['message_id'],
                sender=f"{email_data['sender']['name']} <{email_data['sender']['email']}>",
                recipient=f"{email_data['recipient']['name']} <{email_data['recipient']['email']}>",
                subject=email_data['subject'],
                received_at=datetime.now(timezone.utc),
                status='received'
            )

            # Add to database
            self.db_session.add(email_log)
            self.db_session.commit()

            logger.info(f"Processed email: {email_log.id} - {email_log.subject}")
            return email_log
        except Exception as e:
            logger.error(f"Error processing email: {str(e)}")
            self.db_session.rollback()
            return None

    def get_email_content(self, email_data: Dict) -> str:
        """
        Extract the content from email data for AI processing.

        Args:
            email_data: Parsed email data

        Returns:
            str: Email content for AI processing
        """
        # Prefer plain text over HTML for AI processing
        if email_data['body']['text']:
            return email_data['body']['text']
        elif email_data['body']['html']:
            # Simple HTML to text conversion
            # In a production system, use a proper HTML to text converter
            text = re.sub('<[^<]+?>', '', email_data['body']['html'])
            return text
        else:
            return f"Subject: {email_data['subject']}\nNo body content available."


def monitor_emails(config: Dict, db_session) -> None:
    """
    Main function to monitor emails.

    Args:
        config: Configuration dictionary with IMAP settings
        db_session: Database session
    """
    monitor = EmailMonitor(
        host=config['imap_host'],
        username=config['imap_username'],
        password=config['imap_password'],
        port=config.get('imap_port', 993),
        use_ssl=config.get('use_ssl', True),
        mailbox=config.get('mailbox', 'INBOX')
    )

    try:
        # Connect to IMAP server
        if not monitor.connect():
            logger.error("Failed to connect to IMAP server")
            return

        # Select mailbox
        if not monitor.select_mailbox():
            logger.error("Failed to select mailbox")
            monitor.disconnect()
            return

        # Search for unread emails
        email_ids = monitor.search_emails('UNSEEN')
        logger.info(f"Found {len(email_ids)} unread emails")

        # Process each email
        for email_id in email_ids:
            # Fetch email
            email_data = monitor.fetch_email(email_id)
            if not email_data:
                logger.error(f"Failed to fetch email {email_id}")
                continue

            # Process email (store in database)
            processor = EmailProcessor(db_session, config.get('allowed_sender_email'))
            email_log = processor.process_email(email_data)

            if email_log:
                # Mark email as read
                monitor.mark_as_read(email_id)
                logger.info(f"Successfully processed email {email_id}")
            else:
                logger.warning(f"Failed to process email {email_id}")

    except Exception as e:
        logger.error(f"Error in email monitoring: {str(e)}")
    finally:
        # Disconnect from IMAP server
        monitor.disconnect()
