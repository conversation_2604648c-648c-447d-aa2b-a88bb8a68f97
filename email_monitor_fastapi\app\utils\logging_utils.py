import logging
import os
import sys
import json
from logging.handlers import RotatingFileHandler
from typing import Dict, Optional
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import time

# Configure logging format
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_LEVEL = logging.INFO
LOG_DIR = 'logs'
LOG_FILE_MAX_SIZE = 10 * 1024 * 1024  # 10 MB
LOG_BACKUP_COUNT = 5

def setup_logging(app_name: str, log_dir: Optional[str] = None) -> logging.Logger:
    """
    Set up logging configuration for the application.
    
    Args:
        app_name: Name of the application (used for logger name and log file)
        log_dir: Directory to store log files (default: 'logs' in current directory)
        
    Returns:
        logging.Logger: Configured logger instance
    """
    # Create logger
    logger = logging.getLogger(app_name)
    logger.setLevel(LOG_LEVEL)
    
    # Create formatters
    formatter = logging.Formatter(LOG_FORMAT)
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # Create file handler if log_dir is provided
    if log_dir:
        # Create log directory if it doesn't exist
        os.makedirs(log_dir, exist_ok=True)
        
        # Create rotating file handler
        log_file = os.path.join(log_dir, f"{app_name}.log")
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=LOG_FILE_MAX_SIZE,
            backupCount=LOG_BACKUP_COUNT
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

class ErrorHandler:
    """
    Class for handling and logging errors consistently across the application.
    """
    
    def __init__(self, logger: logging.Logger):
        """
        Initialize the error handler.
        
        Args:
            logger: Logger instance
        """
        self.logger = logger
    
    def handle_error(self, error: Exception, context: str, retry_func=None, retry_args=None, max_retries: int = 3) -> Dict:
        """
        Handle an error by logging it and optionally retrying the operation.
        
        Args:
            error: Exception that occurred
            context: Context in which the error occurred
            retry_func: Function to retry (default: None)
            retry_args: Arguments for retry function (default: None)
            max_retries: Maximum number of retry attempts (default: 3)
            
        Returns:
            Dict: Error information including success status, error message, and retry attempts
        """
        error_info = {
            'success': False,
            'error': str(error),
            'context': context,
            'retry_attempts': 0,
            'retry_success': False
        }
        
        # Log the error
        self.logger.error(f"Error in {context}: {str(error)}", exc_info=True)
        
        # Retry if a retry function is provided
        if retry_func and retry_args:
            for attempt in range(max_retries):
                try:
                    self.logger.info(f"Retry attempt {attempt + 1}/{max_retries} for {context}")
                    result = retry_func(*retry_args)
                    error_info['retry_success'] = True
                    error_info['retry_attempts'] = attempt + 1
                    self.logger.info(f"Retry successful on attempt {attempt + 1}")
                    return error_info
                except Exception as retry_error:
                    self.logger.error(f"Retry attempt {attempt + 1} failed: {str(retry_error)}")
                    error_info['retry_attempts'] = attempt + 1
        
        return error_info
    
    def log_operation(self, operation: str, details: Dict = None) -> None:
        """
        Log an operation with optional details.
        
        Args:
            operation: Operation being performed
            details: Optional details about the operation (default: None)
        """
        if details:
            self.logger.info(f"{operation}: {json.dumps(details)}")
        else:
            self.logger.info(operation)
    
    def log_success(self, operation: str, details: Dict = None) -> None:
        """
        Log a successful operation with optional details.
        
        Args:
            operation: Operation that succeeded
            details: Optional details about the success (default: None)
        """
        if details:
            self.logger.info(f"SUCCESS - {operation}: {json.dumps(details)}")
        else:
            self.logger.info(f"SUCCESS - {operation}")
    
    def log_warning(self, message: str, details: Dict = None) -> None:
        """
        Log a warning with optional details.
        
        Args:
            message: Warning message
            details: Optional details about the warning (default: None)
        """
        if details:
            self.logger.warning(f"{message}: {json.dumps(details)}")
        else:
            self.logger.warning(message)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for logging HTTP requests and responses.
    """
    
    def __init__(self, app, logger: logging.Logger):
        super().__init__(app)
        self.logger = logger
    
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        # Log request
        self.logger.info(f"Request: {request.method} {request.url}")
        
        # Process request
        response = await call_next(request)
        
        # Calculate processing time
        process_time = time.time() - start_time
        
        # Log response
        self.logger.info(f"Response: {response.status_code} - {process_time:.3f}s")
        
        return response
