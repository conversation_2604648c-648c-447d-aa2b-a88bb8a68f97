/* Orange Theme CSS - Email Monitor Agent Dashboard */

/* Color Variables */
:root {
    --primary-orange: #F47C20;
    --primary-blue: #0B2A5A;
    --white: #FFFFFF;
    --medium-gray: #6E7C8E;
    --dark-gray: #2E2E2E;
    --light-gray: #F8F9FA;
    --border-gray: #E9ECEF;
    --success-green: #28A745;
    --warning-yellow: #FFC107;
    --danger-red: #DC3545;
    --info-cyan: #17A2B8;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--light-gray);
    color: var(--dark-gray);
    line-height: 1.6;
}

/* Navigation */
.navbar {
    background: var(--primary-blue);
    color: var(--white);
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(11, 42, 90, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.nav-brand i {
    color: var(--primary-orange);
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: var(--white);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--primary-orange);
    color: var(--white);
}

.nav-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: rgba(244, 124, 32, 0.1);
    border-radius: 20px;
    border: 1px solid var(--primary-orange);
}

.status-indicator i {
    color: var(--success-green);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Main Content */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.page-header h1 {
    color: var(--dark-gray);
    font-size: 2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-header h1 i {
    color: var(--primary-orange);
}

.page-header p {
    color: var(--medium-gray);
    margin-top: 0.5rem;
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-orange);
    color: var(--white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary:hover {
    background-color: #e06b1a;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(244, 124, 32, 0.3);
}

.btn-secondary {
    background-color: var(--white);
    color: var(--medium-gray);
    border: 1px solid var(--border-gray);
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-secondary:hover {
    background-color: var(--light-gray);
    border-color: var(--primary-orange);
    color: var(--primary-orange);
}

.btn-refresh {
    background: none;
    border: none;
    color: var(--primary-orange);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn-refresh:hover {
    background-color: rgba(244, 124, 32, 0.1);
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-left: 4px solid var(--primary-orange);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
}

.stat-icon.emails {
    background: linear-gradient(135deg, var(--primary-orange), #ff8c42);
}

.stat-icon.whatsapp {
    background: linear-gradient(135deg, #25D366, #128C7E);
}

.stat-icon.replies {
    background: linear-gradient(135deg, var(--info-cyan), #0056b3);
}

.stat-icon.employees {
    background: linear-gradient(135deg, var(--primary-orange), #ff6b35);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: var(--medium-gray);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.stat-change.positive {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-green);
}

.stat-change.neutral {
    background-color: rgba(244, 124, 32, 0.1);
    color: var(--primary-orange);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.dashboard-card {
    background: var(--white);
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-blue), #1a4480);
    color: var(--white);
}

.card-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-header h3 i {
    color: var(--primary-orange);
}

.card-content {
    padding: 1.5rem;
}

/* Welcome Message */
.welcome-message {
    background: linear-gradient(135deg, var(--primary-orange), #ff8c42);
    color: var(--white);
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(244, 124, 32, 0.2);
}

.welcome-message h2 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.welcome-message p {
    opacity: 0.9;
    font-size: 1rem;
}

/* Activity List */
.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    padding: 1rem;
    border-bottom: 1px solid var(--border-gray);
    transition: background-color 0.3s ease;
}

.activity-item:hover {
    background-color: var(--light-gray);
}

.activity-item:last-child {
    border-bottom: none;
}

/* Status Chart */
.status-chart {
    space-y: 1rem;
}

.status-item {
    margin-bottom: 1rem;
}

.status-bar {
    width: 100%;
    height: 8px;
    background-color: var(--border-gray);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.status-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.status-fill.processed {
    background: linear-gradient(90deg, var(--success-green), #20c997);
}

.status-fill.pending {
    background: linear-gradient(90deg, var(--warning-yellow), #ffcd39);
}

.status-fill.failed {
    background: linear-gradient(90deg, var(--danger-red), #e55353);
}

.status-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--medium-gray);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-dot.processed {
    background-color: var(--success-green);
}

.status-dot.pending {
    background-color: var(--warning-yellow);
}

.status-dot.failed {
    background-color: var(--danger-red);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-menu {
        gap: 1rem;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
    }
}
