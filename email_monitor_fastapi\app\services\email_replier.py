import logging
import smtplib
import ssl
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import Dict, List, Optional, Union
from datetime import datetime, timezone

from ..models import EmailLog, EmailReply

logger = logging.getLogger(__name__)

class EmailReplier:
    """
    Class for sending auto-replies to email senders.
    """
    
    def __init__(self, 
                 smtp_host: str, 
                 smtp_port: int,
                 username: str,
                 password: str,
                 use_ssl: bool = True,
                 default_sender: Optional[str] = None):
        """
        Initialize the email replier.
        
        Args:
            smtp_host: SMTP server hostname
            smtp_port: SMTP server port
            username: Email account username
            password: Email account password
            use_ssl: Whether to use SSL for connection (default: True)
            default_sender: Default sender email address (default: None, uses username)
        """
        self.smtp_host = smtp_host
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.use_ssl = use_ssl
        self.default_sender = default_sender or username
        
    def send_reply(self, email_log: EmailLog, db_session) -> Optional[EmailReply]:
        """
        Send an auto-reply to the original email sender.
        
        Args:
            email_log: Processed EmailLog instance
            db_session: Database session
            
        Returns:
            Optional[EmailReply]: Created EmailReply instance or None if sending failed
        """
        if not email_log.auto_reply_text:
            logger.error(f"Email {email_log.id} has no auto-reply text")
            return None
            
        # <AUTHOR> <EMAIL>"
        try:
            reply_to = email_log.sender.split('<')[1].split('>')[0].strip()
        except (IndexError, AttributeError):
            # If the format is not as expected, use the whole string
            reply_to = email_log.sender
            
        # Create reply record
        reply = EmailReply(
            email_log_id=email_log.id,
            reply_to=reply_to,
            subject=f"Re: {email_log.subject}",
            body=email_log.auto_reply_text,
            status='pending'
        )
        
        db_session.add(reply)
        db_session.commit()
        
        # Send reply
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.default_sender
            msg['To'] = reply_to
            msg['Subject'] = f"Re: {email_log.subject}"
            
            # Add body
            msg.attach(MIMEText(email_log.auto_reply_text, 'plain'))
            
            # Connect to SMTP server and send
            if self.use_ssl:
                context = ssl.create_default_context()
                with smtplib.SMTP_SSL(self.smtp_host, self.smtp_port, context=context) as server:
                    server.login(self.username, self.password)
                    server.send_message(msg)
            else:
                with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                    server.starttls()
                    server.login(self.username, self.password)
                    server.send_message(msg)
            
            # Update reply status
            reply.status = 'sent'
            reply.sent_at = datetime.now(timezone.utc)
            db_session.commit()
            
            logger.info(f"Sent email reply to {reply_to}")
            return reply
        except Exception as e:
            logger.error(f"Error sending email reply to {reply_to}: {str(e)}")
            reply.status = 'failed'
            reply.error_message = f"Error: {str(e)}"
            db_session.commit()
            return reply
    
    def retry_failed_replies(self, max_retries: int = 3, db_session=None) -> int:
        """
        Retry sending failed email replies.
        
        Args:
            max_retries: Maximum number of retry attempts
            db_session: Database session
            
        Returns:
            int: Number of successfully retried replies
        """
        # Find failed replies
        failed_replies = db_session.query(EmailReply).filter(EmailReply.status == 'failed').all()
        
        success_count = 0
        
        for reply in failed_replies:
            # Get associated email log
            email_log = db_session.query(EmailLog).filter(EmailLog.id == reply.email_log_id).first()
            if not email_log:
                logger.error(f"Email log {reply.email_log_id} not found for reply {reply.id}")
                continue
                
            # Retry sending
            try:
                # Create message
                msg = MIMEMultipart()
                msg['From'] = self.default_sender
                msg['To'] = reply.reply_to
                msg['Subject'] = reply.subject
                
                # Add body
                msg.attach(MIMEText(reply.body, 'plain'))
                
                # Connect to SMTP server and send
                if self.use_ssl:
                    context = ssl.create_default_context()
                    with smtplib.SMTP_SSL(self.smtp_host, self.smtp_port, context=context) as server:
                        server.login(self.username, self.password)
                        server.send_message(msg)
                else:
                    with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                        server.starttls()
                        server.login(self.username, self.password)
                        server.send_message(msg)
                
                # Update reply status
                reply.status = 'sent'
                reply.sent_at = datetime.now(timezone.utc)
                db_session.commit()
                
                logger.info(f"Retried email reply to {reply.reply_to}")
                success_count += 1
            except Exception as e:
                logger.error(f"Error retrying email reply {reply.id}: {str(e)}")
                reply.error_message = f"Retry error: {str(e)}"
                db_session.commit()
                
        return success_count


class MockEmailReplier:
    """
    Mock implementation for testing or development environments.
    """
    
    def __init__(self, **kwargs):
        """
        Initialize the mock email replier.
        """
        logger.info("Initialized Mock Email Replier")
        
    def send_reply(self, email_log: EmailLog, db_session) -> Optional[EmailReply]:
        """
        Simulate sending an auto-reply.
        
        Args:
            email_log: Processed EmailLog instance
            db_session: Database session
            
        Returns:
            Optional[EmailReply]: Created EmailReply instance
        """
        # Extract sender email
        try:
            reply_to = email_log.sender.split('<')[1].split('>')[0].strip()
        except (IndexError, AttributeError):
            reply_to = email_log.sender
            
        # Create reply record
        reply = EmailReply(
            email_log_id=email_log.id,
            reply_to=reply_to,
            subject=f"Re: {email_log.subject}",
            body=email_log.auto_reply_text or "This is a mock auto-reply.",
            status='sent',
            sent_at=datetime.now(timezone.utc)
        )
        
        db_session.add(reply)
        db_session.commit()
        
        logger.info(f"Simulated email reply to {reply_to}")
        return reply
    
    def retry_failed_replies(self, max_retries: int = 3, db_session=None) -> int:
        """
        Simulate retrying failed email replies.
        
        Args:
            max_retries: Maximum number of retry attempts
            db_session: Database session
            
        Returns:
            int: Number of successfully retried replies
        """
        return 0


def get_email_replier(config: Dict) -> Union[EmailReplier, MockEmailReplier]:
    """
    Factory function to get the appropriate email replier based on configuration.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Union[EmailReplier, MockEmailReplier]: Email replier instance
    """
    if config.get('mock_email', False):
        return MockEmailReplier()
    else:
        return EmailReplier(
            smtp_host=config['smtp_host'],
            smtp_port=config['smtp_port'],
            username=config['smtp_username'],
            password=config['smtp_password'],
            use_ssl=config.get('smtp_use_ssl', True),
            default_sender=config.get('default_sender')
        )
