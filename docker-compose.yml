version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: email_monitor_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: email_monitor
      POSTGRES_USER: email_agent
      POSTGRES_PASSWORD: secure_password_123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U email_agent -d email_monitor"]
      interval: 10s
      timeout: 5s
      retries: 5

  # pgAdmin (Optional - for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: email_monitor_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      postgres:
        condition: service_healthy

  # Email Monitor Agent (Optional - uncomment to run with Docker)
  # email-agent:
  #   build: .
  #   container_name: email_monitor_agent
  #   restart: unless-stopped
  #   environment:
  #     DATABASE_URL: **********************************************************/email_monitor
  #   volumes:
  #     - ./.env:/app/.env
  #   ports:
  #     - "8000:8000"
  #   depends_on:
  #     postgres:
  #       condition: service_healthy

volumes:
  postgres_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  default:
    name: email_monitor_network
