/**
 * Settings functionality for Email Monitor Agent
 */

// Extend the App object with settings methods
Object.assign(App, {
    // Load settings data
    async loadSettingsData() {
        try {
            this.showLoading();
            
            // Load configuration from API or use defaults
            await this.loadEmailConfiguration();
            await this.loadAIConfiguration();
            await this.loadWhatsAppConfiguration();
            
        } catch (error) {
            console.error('Failed to load settings:', error);
            this.loadDefaultSettings();
        } finally {
            this.hideLoading();
        }
    },
    
    // Load email configuration
    async loadEmailConfiguration() {
        try {
            // In a real implementation, this would come from the API
            // For demo purposes, we'll use environment-like values
            const emailConfig = {
                monitoringEmail: '<EMAIL>',
                allowedSender: '<EMAIL>',
                checkInterval: 5
            };
            
            document.getElementById('monitoringEmail').value = emailConfig.monitoringEmail;
            document.getElementById('allowedSender').value = emailConfig.allowedSender;
            document.getElementById('checkInterval').value = emailConfig.checkInterval;
            
        } catch (error) {
            console.error('Failed to load email configuration:', error);
        }
    },
    
    // Load AI configuration
    async loadAIConfiguration() {
        try {
            // Check AI status
            const aiStatus = await this.checkAIStatus();
            
            const aiConfig = {
                model: 'gpt-4',
                status: aiStatus
            };
            
            document.getElementById('aiModel').value = aiConfig.model;
            
            const statusElement = document.getElementById('aiStatus');
            if (aiConfig.status.connected) {
                statusElement.innerHTML = '<i class="fas fa-circle"></i> Connected';
                statusElement.className = 'status-badge active';
            } else {
                statusElement.innerHTML = '<i class="fas fa-circle"></i> Disconnected';
                statusElement.className = 'status-badge inactive';
            }
            
        } catch (error) {
            console.error('Failed to load AI configuration:', error);
            const statusElement = document.getElementById('aiStatus');
            statusElement.innerHTML = '<i class="fas fa-circle"></i> Error';
            statusElement.className = 'status-badge inactive';
        }
    },
    
    // Load WhatsApp configuration
    async loadWhatsAppConfiguration() {
        try {
            // Check WhatsApp status
            const whatsappStatus = await this.checkWhatsAppStatus();
            
            const whatsappConfig = {
                businessPhone: '+91 82202 73650',
                status: whatsappStatus
            };
            
            document.getElementById('businessPhone').value = whatsappConfig.businessPhone;
            
            const statusElement = document.getElementById('whatsappStatus');
            if (whatsappConfig.status.connected) {
                statusElement.innerHTML = '<i class="fas fa-circle"></i> Connected';
                statusElement.className = 'status-badge active';
            } else {
                statusElement.innerHTML = '<i class="fas fa-circle"></i> Disconnected';
                statusElement.className = 'status-badge inactive';
            }
            
        } catch (error) {
            console.error('Failed to load WhatsApp configuration:', error);
            const statusElement = document.getElementById('whatsappStatus');
            statusElement.innerHTML = '<i class="fas fa-circle"></i> Error';
            statusElement.className = 'status-badge inactive';
        }
    },
    
    // Check AI status
    async checkAIStatus() {
        try {
            // This would typically test the OpenAI API connection
            // For demo purposes, we'll simulate a check
            return {
                connected: true,
                model: 'gpt-4',
                lastCheck: new Date().toISOString()
            };
        } catch (error) {
            return {
                connected: false,
                error: error.message
            };
        }
    },
    
    // Check WhatsApp status
    async checkWhatsAppStatus() {
        try {
            // This would typically test the Meta WhatsApp API connection
            // For demo purposes, we'll simulate a check
            return {
                connected: true,
                phoneNumber: '+91 82202 73650',
                lastCheck: new Date().toISOString()
            };
        } catch (error) {
            return {
                connected: false,
                error: error.message
            };
        }
    },
    
    // Load default settings
    loadDefaultSettings() {
        // Email settings
        document.getElementById('monitoringEmail').value = '<EMAIL>';
        document.getElementById('allowedSender').value = '<EMAIL>';
        document.getElementById('checkInterval').value = '5';
        
        // AI settings
        document.getElementById('aiModel').value = 'gpt-4';
        document.getElementById('aiStatus').innerHTML = '<i class="fas fa-circle"></i> Unknown';
        document.getElementById('aiStatus').className = 'status-badge inactive';
        
        // WhatsApp settings
        document.getElementById('businessPhone').value = '+91 82202 73650';
        document.getElementById('whatsappStatus').innerHTML = '<i class="fas fa-circle"></i> Unknown';
        document.getElementById('whatsappStatus').className = 'status-badge inactive';
    },
    
    // Save settings
    async saveSettings() {
        try {
            this.showLoading();
            
            const settings = {
                email: {
                    checkInterval: parseInt(document.getElementById('checkInterval').value)
                },
                ai: {
                    model: document.getElementById('aiModel').value
                }
            };
            
            // In a real implementation, this would save to the backend
            // For demo purposes, we'll just show a success message
            
            this.showToast('success', 'Settings Saved', 'Configuration has been updated successfully');
            
            // Update the refresh interval if changed
            if (settings.email.checkInterval !== this.config.refreshInterval / 1000 / 60) {
                this.config.refreshInterval = settings.email.checkInterval * 60 * 1000;
                this.showToast('info', 'Refresh Updated', 
                    `Dashboard will now refresh every ${settings.email.checkInterval} minutes`);
            }
            
        } catch (error) {
            console.error('Failed to save settings:', error);
            this.showToast('error', 'Error', 'Failed to save settings');
        } finally {
            this.hideLoading();
        }
    },
    
    // Test AI connection
    async testAIConnection() {
        try {
            this.showLoading();
            
            // Simulate AI connection test
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const success = Math.random() > 0.2; // 80% success rate for demo
            
            if (success) {
                document.getElementById('aiStatus').innerHTML = '<i class="fas fa-circle"></i> Connected';
                document.getElementById('aiStatus').className = 'status-badge active';
                this.showToast('success', 'AI Connection', 'Successfully connected to OpenAI API');
            } else {
                document.getElementById('aiStatus').innerHTML = '<i class="fas fa-circle"></i> Failed';
                document.getElementById('aiStatus').className = 'status-badge inactive';
                this.showToast('error', 'AI Connection', 'Failed to connect to OpenAI API');
            }
            
        } catch (error) {
            console.error('AI connection test failed:', error);
            this.showToast('error', 'Error', 'AI connection test failed');
        } finally {
            this.hideLoading();
        }
    },
    
    // Test WhatsApp connection
    async testWhatsAppConnection() {
        try {
            this.showLoading();
            
            // Simulate WhatsApp connection test
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const success = Math.random() > 0.1; // 90% success rate for demo
            
            if (success) {
                document.getElementById('whatsappStatus').innerHTML = '<i class="fas fa-circle"></i> Connected';
                document.getElementById('whatsappStatus').className = 'status-badge active';
                this.showToast('success', 'WhatsApp Connection', 'Successfully connected to Meta WhatsApp API');
            } else {
                document.getElementById('whatsappStatus').innerHTML = '<i class="fas fa-circle"></i> Failed';
                document.getElementById('whatsappStatus').className = 'status-badge inactive';
                this.showToast('error', 'WhatsApp Connection', 'Failed to connect to Meta WhatsApp API');
            }
            
        } catch (error) {
            console.error('WhatsApp connection test failed:', error);
            this.showToast('error', 'Error', 'WhatsApp connection test failed');
        } finally {
            this.hideLoading();
        }
    },
    
    // Reset settings to defaults
    resetSettings() {
        if (confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
            this.loadDefaultSettings();
            this.showToast('info', 'Settings Reset', 'All settings have been reset to defaults');
        }
    },
    
    // Export settings
    exportSettings() {
        const settings = {
            email: {
                monitoringEmail: document.getElementById('monitoringEmail').value,
                allowedSender: document.getElementById('allowedSender').value,
                checkInterval: document.getElementById('checkInterval').value
            },
            ai: {
                model: document.getElementById('aiModel').value
            },
            whatsapp: {
                businessPhone: document.getElementById('businessPhone').value
            },
            exportDate: new Date().toISOString()
        };
        
        const dataStr = JSON.stringify(settings, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `email-monitor-settings-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        this.showToast('success', 'Settings Exported', 'Settings have been downloaded as JSON file');
    },
    
    // Import settings
    importSettings() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = (event) => {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const settings = JSON.parse(e.target.result);
                    
                    // Apply imported settings
                    if (settings.email) {
                        if (settings.email.checkInterval) {
                            document.getElementById('checkInterval').value = settings.email.checkInterval;
                        }
                    }
                    
                    if (settings.ai) {
                        if (settings.ai.model) {
                            document.getElementById('aiModel').value = settings.ai.model;
                        }
                    }
                    
                    this.showToast('success', 'Settings Imported', 'Settings have been imported successfully');
                    
                } catch (error) {
                    this.showToast('error', 'Import Error', 'Invalid settings file format');
                }
            };
            
            reader.readAsText(file);
        };
        
        input.click();
    }
});

// Add settings-specific HTML elements and event listeners
document.addEventListener('DOMContentLoaded', () => {
    // Add action buttons to settings page
    const settingsPage = document.getElementById('settings');
    if (settingsPage) {
        const actionsHtml = `
            <div class="settings-actions" style="margin-top: 2rem; display: flex; gap: 1rem; flex-wrap: wrap;">
                <button class="btn-primary" onclick="App.saveSettings()">
                    <i class="fas fa-save"></i> Save Settings
                </button>
                <button class="btn-secondary" onclick="App.testAIConnection()">
                    <i class="fas fa-robot"></i> Test AI Connection
                </button>
                <button class="btn-secondary" onclick="App.testWhatsAppConnection()">
                    <i class="fab fa-whatsapp"></i> Test WhatsApp
                </button>
                <button class="btn-secondary" onclick="App.exportSettings()">
                    <i class="fas fa-download"></i> Export Settings
                </button>
                <button class="btn-secondary" onclick="App.importSettings()">
                    <i class="fas fa-upload"></i> Import Settings
                </button>
                <button class="btn-danger" onclick="App.resetSettings()">
                    <i class="fas fa-undo"></i> Reset to Defaults
                </button>
            </div>
        `;
        
        settingsPage.insertAdjacentHTML('beforeend', actionsHtml);
    }
});
