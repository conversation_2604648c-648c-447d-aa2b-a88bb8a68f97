<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Email Monitor Agent Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/css/orange-theme.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-envelope-open-text"></i>
                <span>Email Monitor Agent</span>
            </div>
            <div class="nav-menu">
                <a href="#dashboard" class="nav-link active" data-tab="dashboard">
                    <i class="fas fa-chart-line"></i> Dashboard
                </a>
                <a href="#emails" class="nav-link" data-tab="emails">
                    <i class="fas fa-inbox"></i> Email Logs
                </a>
                <a href="#employees" class="nav-link" data-tab="employees">
                    <i class="fas fa-users"></i> Employees
                </a>
                <a href="#settings" class="nav-link" data-tab="settings">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </div>
            <div class="nav-status">
                <div class="status-indicator" id="agentStatus">
                    <i class="fas fa-circle"></i>
                    <span>Agent Status</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-content active">
            <div class="page-header">
                <h1><i class="fas fa-chart-line"></i> Dashboard</h1>
                <p>Monitor your email processing and system performance</p>
            </div>

            <!-- Welcome Message -->
            <div class="welcome-message">
                <h2><i class="fas fa-rocket"></i> Welcome to Your Email Monitor Agent Dashboard!</h2>
                <p>Your email monitoring system is up and running. Here's what your agent can do:</p>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon emails">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalEmails">0</h3>
                        <p>Total Emails</p>
                        <span class="stat-change positive" id="emailsChange">+0 today</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon whatsapp">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalNotifications">0</h3>
                        <p>WhatsApp Sent</p>
                        <span class="stat-change positive" id="notificationsChange">+0 today</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon replies">
                        <i class="fas fa-reply"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalReplies">0</h3>
                        <p>Auto Replies</p>
                        <span class="stat-change positive" id="repliesChange">+0 today</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon employees">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalEmployees">0</h3>
                        <p>Employees</p>
                        <span class="stat-change neutral" id="employeesChange">Active</span>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-clock"></i> Recent Email Activity</h3>
                        <button class="btn-refresh" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="card-content">
                        <div id="recentEmails" class="activity-list">
                            <!-- Recent emails will be loaded here -->
                        </div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-pie"></i> Processing Status</h3>
                    </div>
                    <div class="card-content">
                        <div class="status-chart">
                            <div class="status-item">
                                <div class="status-bar">
                                    <div class="status-fill processed" style="width: 0%" id="processedBar"></div>
                                </div>
                                <div class="status-label">
                                    <span class="status-dot processed"></span>
                                    <span>Processed (<span id="processedCount">0</span>)</span>
                                </div>
                            </div>
                            <div class="status-item">
                                <div class="status-bar">
                                    <div class="status-fill pending" style="width: 0%" id="pendingBar"></div>
                                </div>
                                <div class="status-label">
                                    <span class="status-dot pending"></span>
                                    <span>Pending (<span id="pendingCount">0</span>)</span>
                                </div>
                            </div>
                            <div class="status-item">
                                <div class="status-bar">
                                    <div class="status-fill failed" style="width: 0%" id="failedBar"></div>
                                </div>
                                <div class="status-label">
                                    <span class="status-dot failed"></span>
                                    <span>Failed (<span id="failedCount">0</span>)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Email Logs Tab -->
        <div id="emails" class="tab-content">
            <div class="page-header">
                <h1><i class="fas fa-inbox"></i> Email Logs</h1>
                <p>View and manage processed emails with AI summaries</p>
            </div>

            <!-- Filters -->
            <div class="filters-section">
                <div class="filters-grid">
                    <div class="filter-group">
                        <label for="dateFilter">Date Range</label>
                        <select id="dateFilter" class="filter-select">
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                            <option value="all">All Time</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="senderFilter">Sender</label>
                        <input type="text" id="senderFilter" class="filter-input" placeholder="Filter by sender...">
                    </div>
                    <div class="filter-group">
                        <label for="statusFilter">Status</label>
                        <select id="statusFilter" class="filter-select">
                            <option value="all">All Status</option>
                            <option value="processed">Processed</option>
                            <option value="pending">Pending</option>
                            <option value="failed">Failed</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <button class="btn-primary" onclick="applyFilters()">
                            <i class="fas fa-filter"></i> Apply Filters
                        </button>
                        <button class="btn-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i> Clear
                        </button>
                    </div>
                </div>
            </div>

            <!-- Email List -->
            <div class="emails-container">
                <div id="emailsList" class="emails-list">
                    <!-- Email items will be loaded here -->
                </div>
                <div class="pagination" id="emailsPagination">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Employees Tab -->
        <div id="employees" class="tab-content">
            <div class="page-header">
                <h1><i class="fas fa-users"></i> Employee Management</h1>
                <p>Manage WhatsApp recipients for email notifications</p>
                <button class="btn-primary" onclick="openAddEmployeeModal()">
                    <i class="fas fa-plus"></i> Add Employee
                </button>
            </div>

            <!-- Employees List -->
            <div class="employees-container">
                <div id="employeesList" class="employees-grid">
                    <!-- Employee cards will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div id="settings" class="tab-content">
            <div class="page-header">
                <h1><i class="fas fa-cog"></i> Settings</h1>
                <p>Configure your Email Monitor Agent</p>
            </div>

            <div class="settings-grid">
                <div class="settings-card">
                    <h3><i class="fas fa-envelope"></i> Email Configuration</h3>
                    <div class="setting-item">
                        <label>Monitoring Email</label>
                        <input type="email" id="monitoringEmail" readonly>
                    </div>
                    <div class="setting-item">
                        <label>Allowed Sender</label>
                        <input type="email" id="allowedSender" readonly>
                    </div>
                    <div class="setting-item">
                        <label>Check Interval (minutes)</label>
                        <input type="number" id="checkInterval" min="1" max="60" value="5">
                    </div>
                </div>

                <div class="settings-card">
                    <h3><i class="fas fa-robot"></i> AI Configuration</h3>
                    <div class="setting-item">
                        <label>AI Model</label>
                        <select id="aiModel">
                            <option value="gpt-4">GPT-4</option>
                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>AI Status</label>
                        <div class="status-badge" id="aiStatus">
                            <i class="fas fa-circle"></i> Connected
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3><i class="fab fa-whatsapp"></i> WhatsApp Configuration</h3>
                    <div class="setting-item">
                        <label>Business Phone</label>
                        <input type="text" id="businessPhone" readonly>
                    </div>
                    <div class="setting-item">
                        <label>API Status</label>
                        <div class="status-badge" id="whatsappStatus">
                            <i class="fas fa-circle"></i> Connected
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add/Edit Employee Modal -->
    <div id="employeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle"><i class="fas fa-user-plus"></i> Add Employee</h3>
                <button class="modal-close" onclick="closeEmployeeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="employeeForm" class="modal-body">
                <input type="hidden" id="employeeId">
                <div class="form-group">
                    <label for="employeeName">Employee Name</label>
                    <input type="text" id="employeeName" required placeholder="Enter employee name">
                </div>
                <div class="form-group">
                    <label for="employeePhone">WhatsApp Number</label>
                    <input type="tel" id="employeePhone" required placeholder="+1234567890">
                    <small>Include country code (e.g., +91 for India)</small>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary" onclick="closeEmployeeModal()">Cancel</button>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save"></i> Save Employee
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Email Detail Modal -->
    <div id="emailModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3><i class="fas fa-envelope-open"></i> Email Details</h3>
                <button class="modal-close" onclick="closeEmailModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="emailModalContent">
                <!-- Email details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- Scripts -->
    <script src="/js/app.js"></script>
    <script src="/js/dashboard.js"></script>
    <script src="/js/emails.js"></script>
    <script src="/js/employees.js"></script>
    <script src="/js/settings.js"></script>
</body>
</html>
