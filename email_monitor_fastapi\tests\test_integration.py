import pytest
import os
import tempfile
from unittest.mock import Mock, patch
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.models import Base, EmailLog, WhatsAppNotification, EmailReply
from app.services.email_monitor import EmailProcessor, EmailMonitor
from app.services.ai_summarizer import AISummarizer, LocalLLMSummarizer
from app.services.whatsapp_notifier import WhatsAppNotifier
from app.services.email_replier import EmailReplier, MockEmailReplier

class TestEmailMonitorIntegration:
    """Integration tests for the email monitoring system."""
    
    @pytest.fixture
    def db_session(self):
        """Create a test database session."""
        # Create in-memory SQLite database for testing
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        session = SessionLocal()
        
        yield session
        
        session.close()
    
    @pytest.fixture
    def sample_email_data(self):
        """Sample email data for testing."""
        return {
            'message_id': 'test-message-123',
            'subject': 'Test Email Subject',
            'sender': {
                'name': '<PERSON>',
                'email': '<EMAIL>'
            },
            'recipient': {
                'name': 'Test Recipient',
                'email': '<EMAIL>'
            },
            'date': '2024-01-01 12:00:00',
            'body': {
                'text': 'This is a test email body.',
                'html': '<p>This is a test email body.</p>'
            },
            'attachments': []
        }
    
    def test_email_processor_basic_functionality(self, db_session, sample_email_data):
        """Test basic email processing functionality."""
        processor = EmailProcessor(db_session)
        
        # Process email
        email_log = processor.process_email(sample_email_data)
        
        # Verify email was created
        assert email_log is not None
        assert email_log.message_id == 'test-message-123'
        assert email_log.subject == 'Test Email Subject'
        assert email_log.status == 'received'
        
        # Verify email was saved to database
        saved_email = db_session.query(EmailLog).filter_by(message_id='test-message-123').first()
        assert saved_email is not None
        assert saved_email.id == email_log.id
    
    def test_email_processor_sender_filtering(self, db_session, sample_email_data):
        """Test email processor sender filtering."""
        # Test with allowed sender
        processor = EmailProcessor(db_session, allowed_sender_email='<EMAIL>')
        email_log = processor.process_email(sample_email_data)
        assert email_log is not None
        
        # Test with disallowed sender
        processor = EmailProcessor(db_session, allowed_sender_email='<EMAIL>')
        email_log = processor.process_email(sample_email_data)
        assert email_log is None
    
    def test_email_processor_duplicate_handling(self, db_session, sample_email_data):
        """Test that duplicate emails are handled correctly."""
        processor = EmailProcessor(db_session)
        
        # Process email first time
        email_log1 = processor.process_email(sample_email_data)
        assert email_log1 is not None
        
        # Process same email again
        email_log2 = processor.process_email(sample_email_data)
        assert email_log2 is not None
        assert email_log1.id == email_log2.id  # Should return existing email
        
        # Verify only one email in database
        count = db_session.query(EmailLog).filter_by(message_id='test-message-123').count()
        assert count == 1
    
    @patch('app.services.ai_summarizer.openai.ChatCompletion.create')
    def test_ai_summarizer_integration(self, mock_openai, db_session, sample_email_data):
        """Test AI summarizer integration."""
        # Mock OpenAI response
        mock_openai.return_value = Mock(
            choices=[Mock(message=Mock(content="""
SUMMARY:
• This is a test email
• It contains sample content

KEY_INFO:
Sender: John Doe <<EMAIL>>
Subject: Test Email Subject
Dates: None
Tasks: None

WHATSAPP_SUMMARY:
Test email from John Doe about sample content.

AUTO_REPLY:
Thank you for your email. We have received it and will respond shortly.
"""))]
        )
        
        # Create email log
        processor = EmailProcessor(db_session)
        email_log = processor.process_email(sample_email_data)
        
        # Process with AI
        summarizer = AISummarizer(api_key='test-key')
        result = summarizer.process_email(email_log, sample_email_data['body']['text'], db_session)
        
        # Verify results
        assert result['success'] is True
        assert len(result['summary']) > 0
        assert result['whatsapp_summary'] != ''
        assert result['auto_reply'] != ''
        
        # Verify email log was updated
        db_session.refresh(email_log)
        assert email_log.status == 'processed'
        assert email_log.summary is not None
        assert email_log.whatsapp_summary is not None
        assert email_log.auto_reply_text is not None
    
    def test_local_llm_summarizer(self, db_session, sample_email_data):
        """Test local LLM summarizer (placeholder implementation)."""
        # Create email log
        processor = EmailProcessor(db_session)
        email_log = processor.process_email(sample_email_data)
        
        # Process with local LLM
        summarizer = LocalLLMSummarizer(model_path='test-model')
        result = summarizer.process_email(email_log, sample_email_data['body']['text'], db_session)
        
        # Verify results (placeholder implementation)
        assert result['success'] is True
        assert len(result['summary']) > 0
        assert result['whatsapp_summary'] != ''
        assert result['auto_reply'] != ''
    
    @patch('app.services.whatsapp_notifier.requests.post')
    def test_whatsapp_notifier_integration(self, mock_post, db_session, sample_email_data):
        """Test WhatsApp notifier integration."""
        # Mock successful API response
        mock_post.return_value = Mock(
            status_code=200,
            json=lambda: {'messages': [{'id': 'test-message-id'}]}
        )
        
        # Create and process email
        processor = EmailProcessor(db_session)
        email_log = processor.process_email(sample_email_data)
        email_log.whatsapp_summary = "Test WhatsApp summary"
        db_session.commit()
        
        # Send notification
        notifier = WhatsAppNotifier(
            access_token='test-token',
            phone_number_id='test-phone-id',
            team_numbers=['+1234567890']
        )
        notifications = notifier.send_notification(email_log, db_session)
        
        # Verify notification was created and sent
        assert len(notifications) == 1
        assert notifications[0].status == 'sent'
        assert notifications[0].recipient == '+1234567890'
        
        # Verify API was called
        mock_post.assert_called_once()
    
    def test_mock_email_replier(self, db_session, sample_email_data):
        """Test mock email replier."""
        # Create and process email
        processor = EmailProcessor(db_session)
        email_log = processor.process_email(sample_email_data)
        email_log.auto_reply_text = "Test auto-reply"
        db_session.commit()
        
        # Send reply
        replier = MockEmailReplier()
        reply = replier.send_reply(email_log, db_session)
        
        # Verify reply was created
        assert reply is not None
        assert reply.status == 'sent'
        assert reply.reply_to == '<EMAIL>'
        assert reply.subject == 'Re: Test Email Subject'
    
    def test_end_to_end_workflow(self, db_session, sample_email_data):
        """Test complete end-to-end workflow."""
        # 1. Process email
        processor = EmailProcessor(db_session)
        email_log = processor.process_email(sample_email_data)
        assert email_log is not None
        
        # 2. Simulate AI processing
        email_log.summary = "• Test email summary"
        email_log.whatsapp_summary = "Test WhatsApp summary"
        email_log.auto_reply_text = "Test auto-reply"
        email_log.status = 'processed'
        db_session.commit()
        
        # 3. Send WhatsApp notification (mock)
        with patch('app.services.whatsapp_notifier.requests.post') as mock_post:
            mock_post.return_value = Mock(
                status_code=200,
                json=lambda: {'messages': [{'id': 'test-message-id'}]}
            )
            
            notifier = WhatsAppNotifier(
                access_token='test-token',
                phone_number_id='test-phone-id',
                team_numbers=['+1234567890']
            )
            notifications = notifier.send_notification(email_log, db_session)
            assert len(notifications) == 1
            assert notifications[0].status == 'sent'
        
        # 4. Send email reply (mock)
        replier = MockEmailReplier()
        reply = replier.send_reply(email_log, db_session)
        assert reply is not None
        assert reply.status == 'sent'
        
        # 5. Verify final state
        db_session.refresh(email_log)
        assert email_log.status == 'processed'
        
        # Verify all records were created
        notification_count = db_session.query(WhatsAppNotification).count()
        reply_count = db_session.query(EmailReply).count()
        assert notification_count == 1
        assert reply_count == 1
