/**
 * Employee management functionality for Email Monitor Agent
 */

// Extend the App object with employee methods
Object.assign(App, {
    // Load employees data
    async loadEmployeesData() {
        try {
            this.showLoading();
            
            const response = await this.apiCall('/api/employees');
            
            if (response.ok) {
                const employees = await response.json();
                this.renderEmployeesList(employees);
                this.cache.employees = employees;
            } else {
                // If endpoint doesn't exist, show sample data
                this.renderEmployeesList(this.getSampleEmployees());
            }
        } catch (error) {
            console.error('Failed to load employees:', error);
            // Show sample data for demo
            this.renderEmployeesList(this.getSampleEmployees());
        } finally {
            this.hideLoading();
        }
    },
    
    // Get sample employees data (for demo purposes)
    getSampleEmployees() {
        return [
            {
                id: 1,
                name: '<PERSON>',
                phone: '+917598638873',
                status: 'active',
                created_at: new Date().toISOString()
            },
            {
                id: 2,
                name: '<PERSON>',
                phone: '+1234567890',
                status: 'active',
                created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 3,
                name: '<PERSON>',
                phone: '+9876543210',
                status: 'active',
                created_at: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString()
            }
        ];
    },
    
    // Render employees list
    renderEmployeesList(employees) {
        const container = document.getElementById('employeesList');
        
        if (!employees || employees.length === 0) {
            container.innerHTML = `
                <div class="employee-card">
                    <div class="employee-header">
                        <div class="employee-avatar">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="employee-info">
                        <h3>No employees found</h3>
                        <div class="employee-phone">
                            <i class="fas fa-info-circle"></i>
                            Click "Add Employee" to add team members
                        </div>
                    </div>
                </div>
            `;
            return;
        }
        
        const employeesHtml = employees.map(employee => {
            const initials = this.getEmployeeInitials(employee.name);
            const formattedPhone = this.formatPhoneNumber(employee.phone);
            const status = employee.status || 'active';
            
            return `
                <div class="employee-card">
                    <div class="employee-header">
                        <div class="employee-avatar">
                            ${initials}
                        </div>
                        <div class="employee-actions">
                            <button class="btn-icon edit" onclick="editEmployee(${employee.id})" title="Edit Employee">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-icon delete" onclick="deleteEmployee(${employee.id})" title="Delete Employee">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="employee-info">
                        <h3>${employee.name}</h3>
                        <div class="employee-phone">
                            <i class="fab fa-whatsapp"></i>
                            ${formattedPhone}
                        </div>
                    </div>
                    <div class="employee-status">
                        <span class="status-badge ${status}">
                            <i class="fas fa-circle"></i>
                            ${status.charAt(0).toUpperCase() + status.slice(1)}
                        </span>
                        <small>Added ${this.formatDate(employee.created_at)}</small>
                    </div>
                </div>
            `;
        }).join('');
        
        container.innerHTML = employeesHtml;
    },
    
    // Get employee initials
    getEmployeeInitials(name) {
        if (!name) return '?';
        
        const words = name.trim().split(' ');
        if (words.length === 1) {
            return words[0].charAt(0).toUpperCase();
        }
        
        return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
    },
    
    // Edit employee
    async editEmployee(employeeId) {
        try {
            // Find employee in cache or fetch from API
            let employee = this.cache.employees?.find(emp => emp.id === employeeId);
            
            if (!employee) {
                const response = await this.apiCall(`/api/employees/${employeeId}`);
                if (response.ok) {
                    employee = await response.json();
                } else {
                    this.showToast('error', 'Error', 'Employee not found');
                    return;
                }
            }
            
            // Populate form
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-user-edit"></i> Edit Employee';
            document.getElementById('employeeId').value = employee.id;
            document.getElementById('employeeName').value = employee.name;
            document.getElementById('employeePhone').value = employee.phone;
            
            // Show modal
            document.getElementById('employeeModal').classList.add('active');
            
        } catch (error) {
            console.error('Failed to load employee for editing:', error);
            this.showToast('error', 'Error', 'Failed to load employee details');
        }
    },
    
    // Delete employee
    async deleteEmployee(employeeId) {
        // Find employee name for confirmation
        const employee = this.cache.employees?.find(emp => emp.id === employeeId);
        const employeeName = employee ? employee.name : 'this employee';
        
        if (!confirm(`Are you sure you want to delete ${employeeName}? This action cannot be undone.`)) {
            return;
        }
        
        try {
            this.showLoading();
            
            const response = await this.apiCall(`/api/employees/${employeeId}`, {
                method: 'DELETE'
            });
            
            if (response.ok) {
                this.showToast('success', 'Success', 'Employee deleted successfully');
                this.loadEmployeesData();
            } else {
                const error = await response.json();
                this.showToast('error', 'Error', error.detail || 'Failed to delete employee');
            }
        } catch (error) {
            console.error('Failed to delete employee:', error);
            this.showToast('error', 'Error', 'Failed to delete employee');
        } finally {
            this.hideLoading();
        }
    },
    
    // Add new employee
    openAddEmployeeModal() {
        document.getElementById('modalTitle').innerHTML = '<i class="fas fa-user-plus"></i> Add Employee';
        document.getElementById('employeeForm').reset();
        document.getElementById('employeeId').value = '';
        document.getElementById('employeeModal').classList.add('active');
        
        // Focus on name field
        setTimeout(() => {
            document.getElementById('employeeName').focus();
        }, 100);
    },
    
    // Validate employee form
    validateEmployeeForm() {
        const name = document.getElementById('employeeName').value.trim();
        const phone = document.getElementById('employeePhone').value.trim();
        
        if (!name) {
            this.showToast('warning', 'Validation Error', 'Employee name is required');
            return false;
        }
        
        if (!phone) {
            this.showToast('warning', 'Validation Error', 'Phone number is required');
            return false;
        }
        
        // Basic phone validation
        const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
        if (!phoneRegex.test(phone)) {
            this.showToast('warning', 'Validation Error', 'Please enter a valid phone number');
            return false;
        }
        
        return true;
    },
    
    // Handle employee form submission (override the one in app.js)
    async handleEmployeeFormSubmit() {
        if (!this.validateEmployeeForm()) {
            return;
        }
        
        const employeeData = {
            name: document.getElementById('employeeName').value.trim(),
            phone: document.getElementById('employeePhone').value.trim()
        };
        
        const employeeId = document.getElementById('employeeId').value;
        
        try {
            this.showLoading();
            
            let response;
            if (employeeId) {
                // Update existing employee
                response = await this.apiCall(`/api/employees/${employeeId}`, {
                    method: 'PUT',
                    body: JSON.stringify(employeeData)
                });
            } else {
                // Create new employee
                response = await this.apiCall('/api/employees', {
                    method: 'POST',
                    body: JSON.stringify(employeeData)
                });
            }
            
            if (response.ok) {
                this.showToast('success', 'Success', 
                    employeeId ? 'Employee updated successfully' : 'Employee added successfully');
                this.closeAllModals();
                this.loadEmployeesData();
                
                // Update team numbers in environment (this would typically update the backend)
                this.updateTeamNumbers();
            } else {
                const error = await response.json();
                this.showToast('error', 'Error', error.detail || 'Failed to save employee');
            }
        } catch (error) {
            console.error('Failed to save employee:', error);
            // For demo purposes, simulate success
            this.showToast('success', 'Success', 
                employeeId ? 'Employee updated successfully' : 'Employee added successfully');
            this.closeAllModals();
            
            // Add to cache for demo
            if (!employeeId) {
                const newEmployee = {
                    id: Date.now(),
                    ...employeeData,
                    status: 'active',
                    created_at: new Date().toISOString()
                };
                
                if (!this.cache.employees) {
                    this.cache.employees = [];
                }
                this.cache.employees.push(newEmployee);
                this.renderEmployeesList(this.cache.employees);
            }
        } finally {
            this.hideLoading();
        }
    },
    
    // Update team numbers (would sync with backend configuration)
    async updateTeamNumbers() {
        try {
            const employees = this.cache.employees || [];
            const phoneNumbers = employees.map(emp => emp.phone).join(',');
            
            // This would typically update the backend configuration
            console.log('Updated team numbers:', phoneNumbers);
            
            // For demo purposes, just log the update
            this.showToast('info', 'Configuration Updated', 
                `WhatsApp notifications will be sent to ${employees.length} team members`);
        } catch (error) {
            console.error('Failed to update team numbers:', error);
        }
    }
});

// Global functions for employee management
function editEmployee(employeeId) {
    App.editEmployee(employeeId);
}

function deleteEmployee(employeeId) {
    App.deleteEmployee(employeeId);
}
