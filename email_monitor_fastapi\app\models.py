import os
from typing import List, Dict, Optional
from datetime import datetime

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker

# Create SQLAlchemy Base
Base = declarative_base()

class EmailLog(Base):
    """Model for storing email processing logs"""
    
    __tablename__ = 'email_logs'
    
    id = Column(Integer, primary_key=True)
    message_id = Column(String(255), unique=True, nullable=False)
    sender = Column(String(255), nullable=False)
    recipient = Column(String(255), nullable=False)
    subject = Column(String(255), nullable=True)
    received_at = Column(DateTime, default=datetime.utcnow)
    processed_at = Column(DateTime, nullable=True)
    summary = Column(Text, nullable=True)
    extracted_data = Column(Text, nullable=True)
    whatsapp_summary = Column(Text, nullable=True)
    auto_reply_text = Column(Text, nullable=True)
    status = Column(String(50), default='received')  # received, processed, failed
    error_message = Column(Text, nullable=True)
    
    # Relationships
    notifications = relationship("WhatsAppNotification", back_populates="email_log")
    replies = relationship("EmailReply", back_populates="email_log")
    
    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'message_id': self.message_id,
            'sender': self.sender,
            'recipient': self.recipient,
            'subject': self.subject,
            'received_at': self.received_at.isoformat() if self.received_at else None,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
            'summary': self.summary,
            'extracted_data': self.extracted_data,
            'whatsapp_summary': self.whatsapp_summary,
            'auto_reply_text': self.auto_reply_text,
            'status': self.status,
            'error_message': self.error_message
        }


class WhatsAppNotification(Base):
    """Model for storing WhatsApp notification logs"""
    
    __tablename__ = 'whatsapp_notifications'
    
    id = Column(Integer, primary_key=True)
    email_log_id = Column(Integer, ForeignKey('email_logs.id'), nullable=False)
    recipient = Column(String(50), nullable=False)  # Phone number
    message = Column(Text, nullable=False)
    sent_at = Column(DateTime, nullable=True)
    status = Column(String(50), default='pending')  # pending, sent, delivered, failed
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)
    
    # Relationship
    email_log = relationship("EmailLog", back_populates="notifications")
    
    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'email_log_id': self.email_log_id,
            'recipient': self.recipient,
            'message': self.message,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'status': self.status,
            'error_message': self.error_message,
            'retry_count': self.retry_count
        }


class EmailReply(Base):
    """Model for storing email reply logs"""
    
    __tablename__ = 'email_replies'
    
    id = Column(Integer, primary_key=True)
    email_log_id = Column(Integer, ForeignKey('email_logs.id'), nullable=False)
    reply_to = Column(String(255), nullable=False)
    subject = Column(String(255), nullable=True)
    body = Column(Text, nullable=False)
    sent_at = Column(DateTime, nullable=True)
    status = Column(String(50), default='pending')  # pending, sent, failed
    error_message = Column(Text, nullable=True)
    
    # Relationship
    email_log = relationship("EmailLog", back_populates="replies")
    
    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'email_log_id': self.email_log_id,
            'reply_to': self.reply_to,
            'subject': self.subject,
            'body': self.body,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'status': self.status,
            'error_message': self.error_message
        }


# Database setup function
def get_db():
    """
    Create database engine and session
    """
    SQLALCHEMY_DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./email_monitor.db")
    engine = create_engine(SQLALCHEMY_DATABASE_URL)
    
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    # Create session
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()
