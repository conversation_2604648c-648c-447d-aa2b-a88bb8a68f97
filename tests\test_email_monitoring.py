#!/usr/bin/env python3
"""
Email Monitoring Test Script

This script tests the actual email monitoring functionality with your credentials.
It will check for emails and process them through the pipeline (without OpenAI).
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add app to path
sys.path.append('.')

def test_email_monitoring():
    """Test actual email monitoring with real credentials."""
    print("🚀 Testing Email Monitoring with Real Credentials")
    print("=" * 60)

    try:
        from app.services.email_monitor import EmailMonitor, EmailProcessor
        from app.models import get_db

        # Get configuration
        config = {
            'imap_host': os.getenv("IMAP_HOST"),
            'imap_port': int(os.getenv("IMAP_PORT", "993")),
            'imap_username': os.getenv("IMAP_USERNAME"),
            'imap_password': os.getenv("IMAP_PASSWORD"),
            'use_ssl': os.getenv("IMAP_USE_SSL", "True").lower() == "true",
            'mailbox': os.getenv("IMAP_MAILBOX", "INBOX"),
            'allowed_sender_email': os.getenv("ALLOWED_SENDER_EMAIL")
        }

        print(f"📧 Monitoring: {config['imap_username']}")
        print(f"🔍 Allowed sender: {config['allowed_sender_email']}")
        print(f"📬 Mailbox: {config['mailbox']}")

        # Create email monitor
        monitor = EmailMonitor(
            host=config['imap_host'],
            username=config['imap_username'],
            password=config['imap_password'],
            port=config['imap_port'],
            use_ssl=config['use_ssl'],
            mailbox=config['mailbox']
        )

        # Connect to email server
        print("\n🔌 Connecting to email server...")
        if not monitor.connect():
            print("❌ Failed to connect to email server")
            return False

        print("✅ Connected to email server successfully!")

        # Select mailbox
        if not monitor.select_mailbox():
            print("❌ Failed to select mailbox")
            monitor.disconnect()
            return False

        print("✅ Mailbox selected successfully!")

        # Search for emails
        print("\n🔍 Searching for emails...")

        # Get all emails
        all_emails = monitor.search_emails('ALL')
        print(f"📊 Total emails in mailbox: {len(all_emails)}")

        # Get unread emails
        unread_emails = monitor.search_emails('UNSEEN')
        print(f"📊 Unread emails: {len(unread_emails)}")

        # Get recent emails (last 5)
        recent_emails = all_emails[-5:] if len(all_emails) >= 5 else all_emails
        print(f"📊 Processing last {len(recent_emails)} emails...")

        # Database setup
        db = get_db()
        processor = EmailProcessor(db, config['allowed_sender_email'])

        processed_count = 0
        filtered_count = 0

        # Process recent emails
        for i, email_id in enumerate(recent_emails, 1):
            print(f"\n📧 Processing email {i}/{len(recent_emails)} (ID: {email_id})")

            # Fetch email
            email_data = monitor.fetch_email(email_id)
            if not email_data:
                print(f"   ❌ Failed to fetch email {email_id}")
                continue

            print(f"   📨 From: {email_data['sender']['email']}")
            print(f"   📝 Subject: {email_data['subject'][:50]}...")
            print(f"   📅 Date: {email_data['date']}")

            # Process email (this will check sender filtering)
            email_log = processor.process_email(email_data)

            if email_log:
                processed_count += 1
                print(f"   ✅ Email processed and stored in database (ID: {email_log.id})")

                # Add some mock AI processing results for demonstration
                email_log.summary = f"• Email from {email_data['sender']['email']}\n• Subject: {email_data['subject']}"
                email_log.whatsapp_summary = f"New email from {email_data['sender']['name'] or email_data['sender']['email']}: {email_data['subject'][:50]}..."
                email_log.auto_reply_text = "Thank you for your email. We have received it and will respond shortly."
                email_log.status = 'processed'
                db.commit()

                print(f"   📝 Added mock AI processing results")
            else:
                filtered_count += 1
                print(f"   🔍 Email filtered out (not from allowed sender)")

        # Summary
        print(f"\n📊 Processing Summary:")
        print(f"   ✅ Emails processed: {processed_count}")
        print(f"   🔍 Emails filtered: {filtered_count}")
        print(f"   📧 Total emails checked: {len(recent_emails)}")

        # Show database contents
        from app.models import EmailLog
        total_in_db = db.query(EmailLog).count()
        print(f"   🗄️ Total emails in database: {total_in_db}")

        # Show recent processed emails
        recent_processed = db.query(EmailLog).order_by(EmailLog.received_at.desc()).limit(3).all()
        if recent_processed:
            print(f"\n📋 Recent processed emails:")
            for email in recent_processed:
                print(f"   - {email.sender} | {email.subject} | {email.status}")

        db.close()
        monitor.disconnect()

        print(f"\n✅ Email monitoring test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Email monitoring test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_whatsapp_notification():
    """Test WhatsApp notification with mock data."""
    print("\n📱 Testing WhatsApp Notification...")

    try:
        from app.services.whatsapp_notifier import get_whatsapp_notifier
        from app.models import get_db, EmailLog

        # Get configuration
        config = {
            'meta_api_token': os.getenv("META_API_TOKEN"),
            'meta_phone_number_id': os.getenv("META_PHONE_NUMBER_ID"),
            'team_numbers': os.getenv("TEAM_NUMBERS", "").split(","),
        }

        # Create a test email log
        db = get_db()
        test_email = EmailLog(
            message_id='test-whatsapp-' + str(os.urandom(4).hex()),
            sender='Test Sender <<EMAIL>>',
            recipient='<EMAIL>',
            subject='WhatsApp Notification Test',
            whatsapp_summary='🧪 This is a test WhatsApp notification from the Email Monitor Agent!',
            status='processed'
        )

        db.add(test_email)
        db.commit()

        print(f"   📧 Created test email: {test_email.subject}")
        print(f"   📱 Sending to: {config['team_numbers']}")

        # Get WhatsApp notifier
        notifier = get_whatsapp_notifier(config)

        # Refresh the test_email to get the ID
        db.refresh(test_email)

        # Send notification
        notifications = notifier.send_notification(test_email, db)

        if notifications:
            print(f"   ✅ WhatsApp notification sent successfully!")
            for notification in notifications:
                print(f"      📱 To: {notification.recipient} | Status: {notification.status}")
        else:
            print(f"   ❌ No notifications were sent")

        # Clean up test data
        db.delete(test_email)
        db.commit()
        db.close()

        return len(notifications) > 0

    except Exception as e:
        print(f"   ❌ WhatsApp notification test failed: {str(e)}")
        return False

def test_email_reply():
    """Test email auto-reply with mock data."""
    print("\n📤 Testing Email Auto-Reply...")

    try:
        from app.services.email_replier import get_email_replier
        from app.models import get_db, EmailLog

        # Get configuration
        config = {
            'smtp_host': os.getenv("SMTP_HOST"),
            'smtp_port': int(os.getenv("SMTP_PORT", "587")),
            'smtp_username': os.getenv("SMTP_USERNAME"),
            'smtp_password': os.getenv("SMTP_PASSWORD"),
            'smtp_use_ssl': os.getenv("SMTP_USE_SSL", "False").lower() == "true",
            'default_sender': os.getenv("DEFAULT_SENDER"),
            'mock_email': True  # Use mock for testing
        }

        # Create a test email log
        db = get_db()
        test_email = EmailLog(
            message_id='test-reply-' + str(os.urandom(4).hex()),
            sender='Test Sender <<EMAIL>>',
            recipient=config['default_sender'],
            subject='Auto-Reply Test',
            auto_reply_text='Thank you for your email. This is an automated response from the Email Monitor Agent.',
            status='processed'
        )

        db.add(test_email)
        db.commit()

        print(f"   📧 Created test email: {test_email.subject}")
        print(f"   📤 Replying to: <EMAIL>")

        # Get email replier (using mock)
        replier = get_email_replier(config)

        # Send reply
        reply = replier.send_reply(test_email, db)

        if reply and reply.status == 'sent':
            print(f"   ✅ Auto-reply sent successfully!")
            print(f"      📤 To: {reply.reply_to} | Subject: {reply.subject}")
        else:
            print(f"   ❌ Auto-reply failed")

        # Clean up test data
        db.delete(test_email)
        if reply:
            db.delete(reply)
        db.commit()
        db.close()

        return reply and reply.status == 'sent'

    except Exception as e:
        print(f"   ❌ Email auto-reply test failed: {str(e)}")
        return False

def main():
    """Run email monitoring tests."""
    print("🧪 Email Monitor Agent - Live Email Monitoring Test")
    print("=" * 60)

    tests = [
        ("Email Monitoring", test_email_monitoring),
        ("WhatsApp Notification", test_whatsapp_notification),
        ("Email Auto-Reply", test_email_reply),
    ]

    results = {}

    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
            results[test_name] = False

    # Summary
    print("\n" + "=" * 60)
    print("📊 EMAIL MONITORING TEST RESULTS")
    print("=" * 60)

    passed = 0
    total = len(results)

    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1

    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")

    if passed >= 2:  # Email monitoring + at least one other
        print("🎉 Email monitoring is working! Your agent can process emails successfully!")
        print("\n📋 What's working:")
        print("✅ Email connection and fetching")
        print("✅ Sender filtering")
        print("✅ Database storage")
        print("✅ WhatsApp notifications (Meta Cloud API)")
        print("✅ Email auto-replies")

        print("\n⚠️  Note: OpenAI API key needs to be updated for AI summarization")
        print("💡 You can run the agent without AI and add OpenAI later")

        print("\n🚀 Ready to start monitoring emails!")
    else:
        print("⚠️  Some core functionality needs attention before running the agent.")

if __name__ == "__main__":
    main()
