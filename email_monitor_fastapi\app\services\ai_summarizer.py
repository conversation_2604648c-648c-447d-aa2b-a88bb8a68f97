import os
import json
import logging
from typing import Dict, <PERSON>, Optional, Tuple, Union

import openai
from ..models import EmailLog
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class AISummarizer:
    """
    Class for AI-powered email summarization and data extraction using OpenAI GPT-4.
    """

    def __init__(self, api_key: str, model: str = "gpt-4"):
        """
        Initialize the AI summarizer.

        Args:
            api_key: OpenAI API key
            model: OpenAI model to use (default: "gpt-4")
        """
        self.api_key = api_key
        self.model = model
        openai.api_key = api_key

    def process_email(self, email_log: EmailLog, email_content: str, db_session) -> Dict:
        """
        Process an email using AI to generate summaries and extract data.

        Args:
            email_log: EmailLog instance
            email_content: Email content to process
            db_session: Database session

        Returns:
            Dict: Processing results including summaries and extracted data
        """
        try:
            # Create prompt for AI
            prompt = self._create_prompt(email_log, email_content)

            # Call OpenAI API
            response = self._call_openai_api(prompt)

            # Parse AI response
            result = self._parse_ai_response(response)

            # Update email log with results
            self._update_email_log(email_log, result, db_session)

            return result
        except Exception as e:
            logger.error(f"Error in AI processing: {str(e)}")
            email_log.status = 'failed'
            email_log.error_message = f"AI processing error: {str(e)}"
            email_log.processed_at = datetime.now(timezone.utc)
            db_session.commit()
            return {
                'success': False,
                'error': str(e)
            }

    def _create_prompt(self, email_log: EmailLog, email_content: str) -> str:
        """
        Create a prompt for the AI model.

        Args:
            email_log: EmailLog instance
            email_content: Email content to process

        Returns:
            str: Formatted prompt for AI
        """
        return f"""
You are an AI assistant that processes emails. Your task is to analyze the following email and provide:

1. A concise summary of the email content (3-5 bullet points)
2. Key information extraction (sender, subject, important dates, tasks/action items)
3. A very brief summary for WhatsApp (max 3 lines)
4. An appropriate auto-reply text

Please format your response exactly as follows:

SUMMARY:
• [First bullet point]
• [Second bullet point]
• [And so on...]

KEY_INFO:
Sender: [Sender name and email]
Subject: [Email subject]
Dates: [Any important dates mentioned]
Tasks: [Any tasks or action items]

WHATSAPP_SUMMARY:
[Very brief summary for WhatsApp, maximum 3 lines]

AUTO_REPLY:
[Appropriate auto-reply text]

Here is the email:
From: {email_log.sender}
Subject: {email_log.subject}
---
{email_content}
"""

    def _call_openai_api(self, prompt: str) -> str:
        """
        Call the OpenAI API with the given prompt.

        Args:
            prompt: Formatted prompt for AI

        Returns:
            str: AI response text
        """
        try:
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an AI assistant that processes emails."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,  # Lower temperature for more consistent outputs
                max_tokens=1000
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"OpenAI API error: {str(e)}")
            raise

    def _parse_ai_response(self, response: str) -> Dict:
        """
        Parse the AI response into structured data.

        Args:
            response: AI response text

        Returns:
            Dict: Structured data from AI response
        """
        result = {
            'success': True,
            'summary': [],
            'key_info': {
                'sender': '',
                'subject': '',
                'dates': [],
                'tasks': []
            },
            'whatsapp_summary': '',
            'auto_reply': ''
        }

        # Extract sections from response
        sections = {
            'SUMMARY': [],
            'KEY_INFO': {},
            'WHATSAPP_SUMMARY': '',
            'AUTO_REPLY': ''
        }

        current_section = None
        for line in response.split('\n'):
            line = line.strip()
            if not line:
                continue

            if line == 'SUMMARY:':
                current_section = 'SUMMARY'
                continue
            elif line == 'KEY_INFO:':
                current_section = 'KEY_INFO'
                continue
            elif line == 'WHATSAPP_SUMMARY:':
                current_section = 'WHATSAPP_SUMMARY'
                continue
            elif line == 'AUTO_REPLY:':
                current_section = 'AUTO_REPLY'
                continue

            if current_section == 'SUMMARY':
                if line.startswith('•') or line.startswith('-'):
                    sections['SUMMARY'].append(line.lstrip('• -').strip())
            elif current_section == 'KEY_INFO':
                if ':' in line:
                    key, value = line.split(':', 1)
                    sections['KEY_INFO'][key.strip().lower()] = value.strip()
            elif current_section == 'WHATSAPP_SUMMARY':
                if sections['WHATSAPP_SUMMARY']:
                    sections['WHATSAPP_SUMMARY'] += '\n'
                sections['WHATSAPP_SUMMARY'] += line
            elif current_section == 'AUTO_REPLY':
                if sections['AUTO_REPLY']:
                    sections['AUTO_REPLY'] += '\n'
                sections['AUTO_REPLY'] += line

        # Populate result
        result['summary'] = sections['SUMMARY']

        if 'sender' in sections['KEY_INFO']:
            result['key_info']['sender'] = sections['KEY_INFO']['sender']
        if 'subject' in sections['KEY_INFO']:
            result['key_info']['subject'] = sections['KEY_INFO']['subject']
        if 'dates' in sections['KEY_INFO']:
            dates_str = sections['KEY_INFO']['dates']
            result['key_info']['dates'] = [d.strip() for d in dates_str.split(',') if d.strip()]
        if 'tasks' in sections['KEY_INFO']:
            tasks_str = sections['KEY_INFO']['tasks']
            result['key_info']['tasks'] = [t.strip() for t in tasks_str.split(',') if t.strip()]

        result['whatsapp_summary'] = sections['WHATSAPP_SUMMARY']
        result['auto_reply'] = sections['AUTO_REPLY']

        return result

    def _update_email_log(self, email_log: EmailLog, result: Dict, db_session) -> None:
        """
        Update the email log with AI processing results.

        Args:
            email_log: EmailLog instance to update
            result: AI processing results
            db_session: Database session
        """
        if result['success']:
            email_log.summary = '\n'.join([f"• {item}" for item in result['summary']])
            email_log.extracted_data = json.dumps(result['key_info'])
            email_log.whatsapp_summary = result['whatsapp_summary']
            email_log.auto_reply_text = result['auto_reply']
            email_log.status = 'processed'
        else:
            email_log.status = 'failed'
            email_log.error_message = result.get('error', 'Unknown AI processing error')

        email_log.processed_at = datetime.now(timezone.utc)
        db_session.commit()


class LocalLLMSummarizer:
    """
    Alternative implementation using a local LLM for email summarization.
    This can be used as a fallback or for environments where OpenAI API is not available.
    """

    def __init__(self, model_path: str):
        """
        Initialize the local LLM summarizer.

        Args:
            model_path: Path to the local LLM model
        """
        self.model_path = model_path
        # In a real implementation, this would load the local LLM
        # For example, using LLaMA or another local model
        logger.info(f"Initialized Local LLM with model: {model_path}")

    def process_email(self, email_log: EmailLog, email_content: str, db_session) -> Dict:
        """
        Process an email using local LLM to generate summaries and extract data.

        Args:
            email_log: EmailLog instance
            email_content: Email content to process
            db_session: Database session

        Returns:
            Dict: Processing results including summaries and extracted data
        """
        # This is a placeholder implementation
        # In a real implementation, this would use the local LLM
        logger.warning("Using placeholder implementation for local LLM")

        result = {
            'success': True,
            'summary': [
                "This is a placeholder summary generated by the local LLM.",
                "In a real implementation, this would use the actual local model."
            ],
            'key_info': {
                'sender': email_log.sender,
                'subject': email_log.subject,
                'dates': [],
                'tasks': []
            },
            'whatsapp_summary': "Placeholder WhatsApp summary for: " + email_log.subject,
            'auto_reply': "Thank you for your email. This is a placeholder auto-reply."
        }

        # Update email log
        email_log.summary = '\n'.join([f"• {item}" for item in result['summary']])
        email_log.extracted_data = json.dumps(result['key_info'])
        email_log.whatsapp_summary = result['whatsapp_summary']
        email_log.auto_reply_text = result['auto_reply']
        email_log.status = 'processed'
        email_log.processed_at = datetime.now(timezone.utc)
        db_session.commit()

        return result


def get_ai_summarizer(config: Dict) -> Union[AISummarizer, LocalLLMSummarizer]:
    """
    Factory function to get the appropriate AI summarizer based on configuration.

    Args:
        config: Configuration dictionary

    Returns:
        Union[AISummarizer, LocalLLMSummarizer]: AI summarizer instance
    """
    if config.get('use_openai', True) and config.get('openai_api_key'):
        return AISummarizer(
            api_key=config['openai_api_key'],
            model=config.get('openai_model', 'gpt-4')
        )
    else:
        return LocalLLMSummarizer(
            model_path=config.get('local_model_path', 'models/llama-7b')
        )
