import logging
import requests
from typing import Dict, List
from datetime import datetime, timezone

from ..models import Em<PERSON><PERSON>og, WhatsAppNotification

logger = logging.getLogger(__name__)

class WhatsAppNotifier:
    """
    Class for sending WhatsApp notifications using Meta Cloud API.
    """
    
    def __init__(self, 
                 access_token: str, 
                 phone_number_id: str,
                 team_numbers: List[str],
                 api_version: str = "v18.0"):
        """
        Initialize the WhatsApp notifier.
        
        Args:
            access_token: Meta Cloud API access token
            phone_number_id: WhatsApp Business phone number ID
            team_numbers: List of team member WhatsApp numbers to notify (without country code prefix)
            api_version: Meta Graph API version (default: v18.0)
        """
        self.access_token = access_token
        self.phone_number_id = phone_number_id
        self.api_version = api_version
        self.base_url = f"https://graph.facebook.com/{api_version}/{phone_number_id}/messages"
        
        # Clean team numbers (remove any prefixes and ensure they have country code)
        self.team_numbers = []
        for number in team_numbers:
            # Remove any whatsapp: prefix if present
            clean_number = number.replace('whatsapp:', '').strip()
            # Ensure number starts with + for international format
            if not clean_number.startswith('+'):
                clean_number = f'+{clean_number}'
            self.team_numbers.append(clean_number)
        
        # Set up headers for API requests
        self.headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }

    def send_notification(self, email_log: EmailLog, db_session) -> List[WhatsAppNotification]:
        """
        Send WhatsApp notifications to team members about a processed email.
        
        Args:
            email_log: Processed EmailLog instance
            db_session: Database session
            
        Returns:
            List[WhatsAppNotification]: List of created WhatsAppNotification instances
        """
        if not email_log.whatsapp_summary:
            logger.error(f"Email {email_log.id} has no WhatsApp summary")
            return []
            
        notifications = []
        
        for recipient in self.team_numbers:
            # Create notification record
            notification = WhatsAppNotification(
                email_log_id=email_log.id,
                recipient=recipient,
                message=email_log.whatsapp_summary,
                status='pending'
            )
            
            db_session.add(notification)
            db_session.commit()
            
            # Send notification via Meta Cloud API
            try:
                payload = {
                    "messaging_product": "whatsapp",
                    "to": recipient,
                    "type": "text",
                    "text": {
                        "body": self._format_message(email_log)
                    }
                }
                
                response = requests.post(
                    self.base_url,
                    headers=self.headers,
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    response_data = response.json()
                    message_id = response_data.get('messages', [{}])[0].get('id', 'unknown')
                    
                    # Update notification status
                    notification.status = 'sent'
                    notification.sent_at = datetime.now(timezone.utc)
                    db_session.commit()
                    
                    logger.info(f"Sent WhatsApp notification to {recipient}: {message_id}")
                    notifications.append(notification)
                else:
                    error_msg = f"Meta API error: {response.status_code} - {response.text}"
                    logger.error(f"Meta API error sending to {recipient}: {error_msg}")
                    notification.status = 'failed'
                    notification.error_message = error_msg
                    notification.retry_count += 1
                    db_session.commit()
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"Request error sending to {recipient}: {str(e)}")
                notification.status = 'failed'
                notification.error_message = f"Request error: {str(e)}"
                notification.retry_count += 1
                db_session.commit()
            except Exception as e:
                logger.error(f"Error sending WhatsApp notification to {recipient}: {str(e)}")
                notification.status = 'failed'
                notification.error_message = f"Error: {str(e)}"
                notification.retry_count += 1
                db_session.commit()
                
        return notifications

    def _format_message(self, email_log: EmailLog) -> str:
        """
        Format the WhatsApp message.
        
        Args:
            email_log: Processed EmailLog instance
            
        Returns:
            str: Formatted WhatsApp message
        """
        return f"""📧 *New Email Summary*
*From:* {email_log.sender.split('<')[0].strip()}
*Subject:* {email_log.subject}

{email_log.whatsapp_summary}"""

    def retry_failed_notifications(self, max_retries: int = 3, db_session=None) -> int:
        """
        Retry sending failed notifications.
        
        Args:
            max_retries: Maximum number of retry attempts
            db_session: Database session
            
        Returns:
            int: Number of successfully retried notifications
        """
        # Find failed notifications with retry count less than max_retries
        failed_notifications = db_session.query(WhatsAppNotification).filter(
            WhatsAppNotification.status == 'failed',
            WhatsAppNotification.retry_count < max_retries
        ).all()
        
        success_count = 0
        
        for notification in failed_notifications:
            # Get associated email log
            email_log = db_session.query(EmailLog).filter(EmailLog.id == notification.email_log_id).first()
            if not email_log:
                logger.error(f"Email log {notification.email_log_id} not found for notification {notification.id}")
                continue
                
            # Retry sending via Meta Cloud API
            try:
                payload = {
                    "messaging_product": "whatsapp",
                    "to": notification.recipient,
                    "type": "text",
                    "text": {
                        "body": self._format_message(email_log)
                    }
                }
                
                response = requests.post(
                    self.base_url,
                    headers=self.headers,
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    response_data = response.json()
                    message_id = response_data.get('messages', [{}])[0].get('id', 'unknown')
                    
                    # Update notification status
                    notification.status = 'sent'
                    notification.sent_at = datetime.now(timezone.utc)
                    notification.retry_count += 1
                    db_session.commit()
                    
                    logger.info(f"Retried WhatsApp notification to {notification.recipient}: {message_id}")
                    success_count += 1
                else:
                    error_msg = f"Meta API error: {response.status_code} - {response.text}"
                    logger.error(f"Error retrying notification {notification.id}: {error_msg}")
                    notification.error_message = f"Retry error: {error_msg}"
                    notification.retry_count += 1
                    db_session.commit()
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"Request error retrying notification {notification.id}: {str(e)}")
                notification.error_message = f"Retry request error: {str(e)}"
                notification.retry_count += 1
                db_session.commit()
            except Exception as e:
                logger.error(f"Error retrying WhatsApp notification {notification.id}: {str(e)}")
                notification.error_message = f"Retry error: {str(e)}"
                notification.retry_count += 1
                db_session.commit()
                
        return success_count


def get_whatsapp_notifier(config: Dict) -> WhatsAppNotifier:
    """
    Factory function to get the WhatsApp notifier based on configuration.
    
    Args:
        config: Configuration dictionary containing:
            - meta_api_token: Meta Cloud API access token
            - meta_phone_number_id: WhatsApp Business phone number ID
            - team_numbers: List of team member WhatsApp numbers
            - api_version: (optional) Meta Graph API version
        
    Returns:
        WhatsAppNotifier: WhatsApp notifier instance using Meta Cloud API
    """
    return WhatsAppNotifier(
        access_token=config['meta_api_token'],
        phone_number_id=config['meta_phone_number_id'],
        team_numbers=config['team_numbers'],
        api_version=config.get('api_version', 'v18.0')
    )
