# Database (PostgreSQL)
DATABASE_URL=postgresql://username:password@localhost:5432/Email_agent

# PostgreSQL Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=Email_agent
POSTGRES_USER=username
POSTGRES_PASSWORD=password

# Email monitoring (IMAP)
IMAP_HOST=imap.example.com
IMAP_PORT=993
IMAP_USERNAME=<EMAIL>
IMAP_PASSWORD=your_password
IMAP_USE_SSL=True
IMAP_MAILBOX=INBOX

# Email filtering - only process emails from this specific sender
# Leave empty to process emails from all senders
ALLOWED_SENDER_EMAIL=<EMAIL>

# AI summarization
USE_OPENAI=True
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
LOCAL_MODEL_PATH=models/llama-7b

# WhatsApp notification (Meta Cloud API)
MOCK_WHATSAPP=False
META_API_TOKEN=your_meta_access_token
META_PHONE_NUMBER_ID=your_whatsapp_business_phone_number_id
TEAM_NUMBERS=+1234567890,+0987654321

# Email reply (SMTP)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_password
SMTP_USE_SSL=False
DEFAULT_SENDER=<EMAIL>
MOCK_EMAIL=False

# Polling interval (seconds)
POLLING_INTERVAL=300
