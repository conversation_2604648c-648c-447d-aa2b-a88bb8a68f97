#!/usr/bin/env python3
"""
Live Credentials Test Script

This script tests the Email Monitor Agent with actual credentials from .env file.
It performs comprehensive testing of all components without sending actual notifications.
"""

import os
import sys
import traceback
from dotenv import load_dotenv
from datetime import datetime, timezone

# Load environment variables
load_dotenv()

# Add app to path
sys.path.append('.')

def test_database_connection():
    """Test PostgreSQL database connection."""
    print("🔍 Testing Database Connection...")

    try:
        from app.models import get_db, EmailLog

        # Test connection
        db = get_db()

        # Try a simple query
        count = db.query(EmailLog).count()
        print(f"✅ Database connection successful!")
        print(f"   📊 Current emails in database: {count}")

        # Test database operations
        test_email = EmailLog(
            message_id='test-connection-' + str(datetime.now().timestamp()),
            sender='<EMAIL>',
            recipient='<EMAIL>',
            subject='Database Connection Test',
            status='received'
        )

        db.add(test_email)
        db.commit()

        # Verify insertion
        saved_email = db.query(EmailLog).filter_by(message_id=test_email.message_id).first()
        if saved_email:
            print(f"✅ Database write/read operations working!")
            # Clean up test data
            db.delete(saved_email)
            db.commit()

        db.close()
        return True

    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        print(f"   💡 Make sure PostgreSQL is running and credentials are correct")
        return False

def test_email_connection():
    """Test IMAP email connection."""
    print("\n📧 Testing Email (IMAP) Connection...")

    try:
        from app.services.email_monitor import EmailMonitor

        # Get credentials from environment
        host = os.getenv("IMAP_HOST")
        username = os.getenv("IMAP_USERNAME")
        password = os.getenv("IMAP_PASSWORD")
        port = int(os.getenv("IMAP_PORT", "993"))
        use_ssl = os.getenv("IMAP_USE_SSL", "True").lower() == "true"

        print(f"   🔧 Connecting to {host}:{port} as {username}")

        # Create monitor and test connection
        monitor = EmailMonitor(
            host=host,
            username=username,
            password=password,
            port=port,
            use_ssl=use_ssl
        )

        # Test connection
        if monitor.connect():
            print(f"✅ IMAP connection successful!")

            # Test mailbox selection
            if monitor.select_mailbox():
                print(f"✅ Mailbox selection successful!")

                # Test email search (just count, don't fetch)
                email_ids = monitor.search_emails('ALL')
                print(f"   📊 Total emails in inbox: {len(email_ids)}")

                # Test unread emails
                unread_ids = monitor.search_emails('UNSEEN')
                print(f"   📊 Unread emails: {len(unread_ids)}")

            monitor.disconnect()
            return True
        else:
            print(f"❌ IMAP connection failed")
            return False

    except Exception as e:
        print(f"❌ Email connection test failed: {str(e)}")
        print(f"   💡 Check your Gmail app password and IMAP settings")
        return False

def test_openai_connection():
    """Test OpenAI API connection."""
    print("\n🤖 Testing OpenAI API Connection...")

    try:
        from openai import OpenAI

        api_key = os.getenv("OPENAI_API_KEY")
        model = os.getenv("OPENAI_MODEL", "gpt-4")

        if not api_key:
            print("❌ OpenAI API key not found in environment")
            return False

        print(f"   🔧 Testing API key with model: {model}")

        # Create client
        client = OpenAI(api_key=api_key)

        # Test with a simple request
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Say 'API test successful' if you can read this."}
            ],
            max_tokens=10,
            temperature=0
        )

        result = response.choices[0].message.content.strip()
        print(f"✅ OpenAI API connection successful!")
        print(f"   🤖 Response: {result}")
        return True

    except Exception as e:
        print(f"❌ OpenAI API test failed: {str(e)}")
        print(f"   💡 Check your OpenAI API key and billing status")
        return False

def test_whatsapp_api():
    """Test Meta WhatsApp Cloud API connection."""
    print("\n📱 Testing Meta WhatsApp Cloud API...")

    try:
        import requests

        access_token = os.getenv("META_API_TOKEN")
        phone_number_id = os.getenv("META_PHONE_NUMBER_ID")

        if not access_token or not phone_number_id:
            print("❌ Meta API credentials not found in environment")
            return False

        print(f"   🔧 Testing API token with phone number ID: {phone_number_id}")

        # Test API by getting phone number info (doesn't send messages)
        url = f"https://graph.facebook.com/v18.0/{phone_number_id}"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }

        response = requests.get(url, headers=headers, timeout=10)

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Meta WhatsApp API connection successful!")
            print(f"   📱 Phone number: {data.get('display_phone_number', 'N/A')}")
            print(f"   🏢 Business name: {data.get('name', 'N/A')}")
            return True
        else:
            print(f"❌ Meta API test failed: {response.status_code}")
            print(f"   📄 Response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Meta WhatsApp API test failed: {str(e)}")
        print(f"   💡 Check your Meta API token and phone number ID")
        return False

def test_smtp_connection():
    """Test SMTP email connection."""
    print("\n📤 Testing SMTP Email Connection...")

    try:
        import smtplib
        import ssl

        host = os.getenv("SMTP_HOST")
        port = int(os.getenv("SMTP_PORT", "587"))
        username = os.getenv("SMTP_USERNAME")
        password = os.getenv("SMTP_PASSWORD")
        use_ssl = os.getenv("SMTP_USE_SSL", "False").lower() == "true"

        print(f"   🔧 Connecting to {host}:{port} as {username}")

        if use_ssl:
            context = ssl.create_default_context()
            server = smtplib.SMTP_SSL(host, port, context=context)
        else:
            server = smtplib.SMTP(host, port)
            server.starttls()

        # Test login
        server.login(username, password)
        print(f"✅ SMTP connection and authentication successful!")

        server.quit()
        return True

    except Exception as e:
        print(f"❌ SMTP connection test failed: {str(e)}")
        print(f"   💡 Check your Gmail app password and SMTP settings")
        return False

def test_configuration():
    """Test configuration completeness."""
    print("\n⚙️ Testing Configuration...")

    required_vars = [
        "DATABASE_URL",
        "IMAP_HOST", "IMAP_USERNAME", "IMAP_PASSWORD",
        "OPENAI_API_KEY",
        "META_API_TOKEN", "META_PHONE_NUMBER_ID", "TEAM_NUMBERS",
        "SMTP_HOST", "SMTP_USERNAME", "SMTP_PASSWORD"
    ]

    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        print(f"❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        return False
    else:
        print(f"✅ All required environment variables are set!")

        # Show configuration summary
        print(f"\n📋 Configuration Summary:")
        print(f"   📧 Email: {os.getenv('IMAP_USERNAME')} -> {os.getenv('ALLOWED_SENDER_EMAIL', 'ALL SENDERS')}")
        print(f"   📱 WhatsApp: {os.getenv('TEAM_NUMBERS')}")
        print(f"   🗄️ Database: {os.getenv('POSTGRES_DB')} @ {os.getenv('POSTGRES_HOST')}")
        print(f"   🤖 AI Model: {os.getenv('OPENAI_MODEL')}")

        return True

def test_sender_filtering():
    """Test sender filtering logic."""
    print("\n🔍 Testing Sender Filtering...")

    try:
        from app.services.email_monitor import EmailProcessor
        from app.models import get_db

        allowed_sender = os.getenv("ALLOWED_SENDER_EMAIL")

        if not allowed_sender:
            print("✅ No sender filtering configured (will process all emails)")
            return True

        print(f"   🔧 Allowed sender: {allowed_sender}")

        # Test with mock data
        db = get_db()
        processor = EmailProcessor(db, allowed_sender)

        # Test allowed sender
        allowed_email_data = {
            'message_id': 'test-allowed-' + str(datetime.now().timestamp()),
            'sender': {'email': allowed_sender, 'name': 'Allowed Sender'},
            'recipient': {'email': '<EMAIL>', 'name': 'Test'},
            'subject': 'Test Email from Allowed Sender',
            'body': {'text': 'Test content', 'html': ''}
        }

        result = processor.process_email(allowed_email_data)
        if result:
            print(f"✅ Allowed sender email processed successfully")
            # Clean up
            db.delete(result)
            db.commit()

        # Test disallowed sender
        disallowed_email_data = {
            'message_id': 'test-disallowed-' + str(datetime.now().timestamp()),
            'sender': {'email': '<EMAIL>', 'name': 'Spam Sender'},
            'recipient': {'email': '<EMAIL>', 'name': 'Test'},
            'subject': 'Spam Email',
            'body': {'text': 'Spam content', 'html': ''}
        }

        result = processor.process_email(disallowed_email_data)
        if result is None:
            print(f"✅ Disallowed sender email correctly filtered out")

        db.close()
        return True

    except Exception as e:
        print(f"❌ Sender filtering test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("🚀 Email Monitor Agent - Live Credentials Test")
    print("=" * 60)

    tests = [
        ("Configuration", test_configuration),
        ("Database Connection", test_database_connection),
        ("Email (IMAP) Connection", test_email_connection),
        ("OpenAI API", test_openai_connection),
        ("Meta WhatsApp API", test_whatsapp_api),
        ("SMTP Connection", test_smtp_connection),
        ("Sender Filtering", test_sender_filtering),
    ]

    results = {}

    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
            traceback.print_exc()
            results[test_name] = False

    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)

    passed = 0
    total = len(results)

    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1

    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 ALL TESTS PASSED! Your Email Monitor Agent is ready to run!")
        print("\n🚀 Next steps:")
        print("1. Start the agent: python run.py")
        print("2. Check API docs: http://localhost:8000/docs")
        print("3. Monitor logs: tail -f logs/email_monitor.log")
    else:
        print("⚠️  Some tests failed. Please fix the issues before running the agent.")
        print("\n💡 Common fixes:")
        print("- Check your Gmail app passwords")
        print("- Verify Meta API token is valid")
        print("- Ensure PostgreSQL is running")
        print("- Check OpenAI API key and billing")

if __name__ == "__main__":
    main()
