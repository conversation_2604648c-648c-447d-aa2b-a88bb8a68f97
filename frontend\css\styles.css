/* Email Monitor Agent Dashboard Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-color: #F47C20;        /* Bright Orange - Buttons, highlights, accents */
    --primary-dark: #E06A0A;         /* Darker orange for hover states */
    --secondary-color: #6E7C8E;      /* Medium Gray - Secondary text and icons */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;

    /* WhatsApp Colors */
    --whatsapp-color: #25d366;
    --whatsapp-dark: #128c7e;

    /* Background Colors */
    --bg-primary: #FFFFFF;           /* White - Text and backgrounds */
    --bg-secondary: #FFFFFF;         /* White - Secondary backgrounds */
    --bg-tertiary: #f8fafc;          /* Very light gray for subtle contrast */
    --bg-dark: #0B2A5A;              /* Light Blue - Header and footer backgrounds */
    --bg-card: #FFFFFF;              /* White - Card backgrounds */

    /* Text Colors */
    --text-primary: #2E2E2E;         /* Dark Gray/Black - Main text color */
    --text-secondary: #6E7C8E;       /* Medium Gray - Secondary text and icons */
    --text-muted: #6E7C8E;           /* Medium Gray - Muted text */
    --text-white: #FFFFFF;           /* White - Text on dark backgrounds */

    /* Border Colors */
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;

    /* Shadow */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
}

/* Navigation */
.navbar {
    background: var(--bg-dark);      /* Light Blue - Header background */
    color: var(--text-white);
    padding: 0;
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-lg);
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.25rem;
    font-weight: 700;
}

.nav-brand i {
    font-size: 1.5rem;
    color: var(--primary-color);     /* Bright Orange accent */
}

.nav-menu {
    display: flex;
    gap: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    font-weight: 500;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-white);
}

.nav-link.active {
    background-color: var(--primary-color);  /* Bright Orange for active nav */
    color: var(--text-white);
}

.nav-status {
    display: flex;
    align-items: center;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
}

.status-indicator i {
    color: var(--success-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Main Content */
.main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-xl);
}

/* Tab System */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Page Header */
.page-header {
    margin-bottom: var(--spacing-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.page-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.page-header h1 i {
    color: var(--primary-color);
}

.page-header p {
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* Buttons */
.btn-primary, .btn-secondary, .btn-danger, .btn-success {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.875rem;
}

.btn-primary {
    background: var(--primary-color);        /* Bright Orange background */
    color: var(--text-white);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: var(--primary-dark);         /* Darker orange on hover */
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--bg-card);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--border-hover);
}

.btn-danger {
    background-color: var(--danger-color);
    color: var(--text-white);
}

.btn-danger:hover {
    background-color: #dc2626;
    transform: translateY(-1px);
}

.btn-success {
    background-color: var(--success-color);
    color: var(--text-white);
}

.btn-success:hover {
    background-color: #059669;
    transform: translateY(-1px);
}

.btn-refresh {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.btn-refresh:hover {
    background-color: var(--bg-tertiary);
    color: var(--primary-color);
}

.btn-refresh i {
    transition: transform var(--transition-fast);
}

.btn-refresh:hover i {
    transform: rotate(180deg);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stat-card {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);        /* Bright Orange accent bar */
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--text-white);
    flex-shrink: 0;
}

.stat-icon.emails {
    background: var(--primary-color);        /* Bright Orange for emails */
}

.stat-icon.whatsapp {
    background: linear-gradient(135deg, var(--whatsapp-color), var(--whatsapp-dark));
}

.stat-icon.replies {
    background: linear-gradient(135deg, var(--info-color), #0891b2);
}

.stat-icon.employees {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-content p {
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

.stat-change {
    font-size: 0.75rem;
    font-weight: 600;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.stat-change.positive {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.stat-change.negative {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.stat-change.neutral {
    background-color: rgba(100, 116, 139, 0.1);
    color: var(--text-secondary);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.dashboard-card {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-header h3 i {
    color: var(--primary-color);
}

.card-content {
    padding: var(--spacing-lg);
}

/* Activity List */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.activity-item:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--border-hover);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: var(--text-white);
    flex-shrink: 0;
}

.activity-icon.email {
    background: var(--primary-color);        /* Bright Orange for email activities */
}

.activity-icon.whatsapp {
    background: linear-gradient(135deg, var(--whatsapp-color), var(--whatsapp-dark));
}

.activity-icon.reply {
    background: linear-gradient(135deg, var(--info-color), #0891b2);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.activity-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.activity-time {
    color: var(--text-muted);
    font-size: 0.75rem;
    white-space: nowrap;
}

/* Status Chart */
.status-chart {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.status-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.status-bar {
    height: 8px;
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.status-fill {
    height: 100%;
    border-radius: var(--radius-sm);
    transition: width var(--transition-normal);
}

.status-fill.processed {
    background: linear-gradient(90deg, var(--success-color), #059669);
}

.status-fill.pending {
    background: linear-gradient(90deg, var(--warning-color), #d97706);
}

.status-fill.failed {
    background: linear-gradient(90deg, var(--danger-color), #dc2626);
}

.status-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-dot.processed {
    background-color: var(--success-color);
}

.status-dot.pending {
    background-color: var(--warning-color);
}

.status-dot.failed {
    background-color: var(--danger-color);
}

/* Filters Section */
.filters-section {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.filter-group label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.filter-select, .filter-input {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: var(--transition-fast);
}

.filter-select:focus, .filter-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(244, 124, 32, 0.1);  /* Bright Orange focus shadow */
}

/* Email List */
.emails-container {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.emails-list {
    display: flex;
    flex-direction: column;
}

.email-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition-fast);
    cursor: pointer;
}

.email-item:hover {
    background-color: var(--bg-tertiary);
}

.email-item:last-child {
    border-bottom: none;
}

.email-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.email-status.processed {
    background-color: var(--success-color);
}

.email-status.pending {
    background-color: var(--warning-color);
}

.email-status.failed {
    background-color: var(--danger-color);
}

.email-content {
    flex: 1;
    min-width: 0;
}

.email-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.email-sender {
    font-weight: 600;
    color: var(--text-primary);
}

.email-time {
    color: var(--text-muted);
    font-size: 0.75rem;
}

.email-subject {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.email-summary {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.email-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-shrink: 0;
}

.email-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.email-badge.whatsapp {
    background-color: rgba(37, 211, 102, 0.1);
    color: var(--whatsapp-color);
}

.email-badge.reply {
    background-color: rgba(6, 182, 212, 0.1);
    color: var(--info-color);
}

/* Employees Grid */
.employees-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.employee-card {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
    position: relative;
}

.employee-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.employee-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.employee-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-color);        /* Bright Orange background */
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-size: 1.25rem;
    font-weight: 600;
}

.employee-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.btn-icon {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.875rem;
}

.btn-icon.edit {
    background-color: rgba(244, 124, 32, 0.1);  /* Bright Orange background */
    color: var(--primary-color);
}

.btn-icon.edit:hover {
    background-color: rgba(244, 124, 32, 0.2);  /* Darker orange on hover */
}

.btn-icon.delete {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.btn-icon.delete:hover {
    background-color: rgba(239, 68, 68, 0.2);
}

.employee-info h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.employee-phone {
    color: var(--text-secondary);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.employee-status {
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.active {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-badge.inactive {
    background-color: rgba(100, 116, 139, 0.1);
    color: var(--text-secondary);
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

.settings-card {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.settings-card h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.settings-card h3 i {
    color: var(--primary-color);
}

.setting-item {
    margin-bottom: var(--spacing-md);
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    display: block;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    font-size: 0.875rem;
}

.setting-item input, .setting-item select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: var(--transition-fast);
}

.setting-item input:focus, .setting-item select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(244, 124, 32, 0.1);  /* Bright Orange focus shadow */
}

.setting-item input[readonly] {
    background-color: var(--bg-tertiary);
    color: var(--text-muted);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    backdrop-filter: blur(4px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

.modal-content.large {
    max-width: 800px;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-20px) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.modal-header h3 i {
    color: var(--primary-color);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
    font-size: 1.25rem;
}

.modal-close:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
}

/* Form Styles */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    font-size: 0.875rem;
}

.form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: var(--transition-fast);
    font-family: inherit;
}

.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(244, 124, 32, 0.1);  /* Bright Orange focus shadow */
}

.form-group small {
    display: block;
    color: var(--text-muted);
    font-size: 0.75rem;
    margin-top: var(--spacing-xs);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.pagination-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    background-color: var(--bg-primary);
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.875rem;
}

.pagination-btn:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--border-hover);
}

.pagination-btn.active {
    background-color: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Loading Overlay */
.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 3000;
    backdrop-filter: blur(2px);
}

.loading-overlay.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    text-align: center;
    color: var(--primary-color);
}

.loading-spinner i {
    font-size: 2rem;
    margin-bottom: var(--spacing-sm);
}

.loading-spinner p {
    font-weight: 500;
    color: var(--text-secondary);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 90px;
    right: var(--spacing-lg);
    z-index: 4000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background: var(--bg-card);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    min-width: 300px;
    animation: toastSlideIn 0.3s ease-out;
    position: relative;
    overflow: hidden;
}

.toast::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 4px;
}

.toast.success::before {
    background-color: var(--success-color);
}

.toast.error::before {
    background-color: var(--danger-color);
}

.toast.warning::before {
    background-color: var(--warning-color);
}

.toast.info::before {
    background-color: var(--info-color);
}

@keyframes toastSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.toast-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.toast-title {
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.toast-close:hover {
    background-color: var(--bg-tertiary);
}

.toast-message {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 var(--spacing-md);
    }

    .nav-menu {
        display: none;
    }

    .main-content {
        padding: var(--spacing-md);
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .page-header h1 {
        font-size: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .stat-card {
        padding: var(--spacing-md);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .stat-content h3 {
        font-size: 1.5rem;
    }

    .filters-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .email-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .email-header {
        width: 100%;
    }

    .email-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .employees-container {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: var(--spacing-md);
    }

    .modal-header, .modal-body {
        padding: var(--spacing-md);
    }

    .toast-container {
        right: var(--spacing-md);
        left: var(--spacing-md);
    }

    .toast {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .nav-brand {
        font-size: 1rem;
    }

    .nav-brand span {
        display: none;
    }

    .main-content {
        padding: var(--spacing-sm);
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .card-header, .card-content {
        padding: var(--spacing-md);
    }

    .activity-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .employee-header {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .employee-actions {
        align-self: flex-end;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--border-hover);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Print Styles */
@media print {
    .navbar,
    .modal,
    .toast-container,
    .loading-overlay {
        display: none !important;
    }

    .main-content {
        padding: 0;
        max-width: none;
    }

    .stat-card,
    .dashboard-card,
    .emails-container,
    .employee-card {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}
