import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from unittest.mock import patch

from app.main import app
from app.models import Base, EmailLog, WhatsAppNotification, EmailReply, get_db

class TestAPIEndpoints:
    """Test FastAPI endpoints functionality."""
    
    @pytest.fixture
    def db_session(self):
        """Create a test database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        session = SessionLocal()
        
        yield session
        
        session.close()
    
    @pytest.fixture
    def client(self, db_session):
        """Create a test client with database dependency override."""
        def override_get_db():
            try:
                yield db_session
            finally:
                pass
        
        app.dependency_overrides[get_db] = override_get_db
        client = TestClient(app)
        yield client
        app.dependency_overrides.clear()
    
    @pytest.fixture
    def sample_email_data(self, db_session):
        """Create sample email data in the database."""
        email_log = EmailLog(
            message_id='test-message-123',
            sender='<PERSON> <<EMAIL>>',
            recipient='Test Recipient <<EMAIL>>',
            subject='Test Email Subject',
            summary='• Test email summary\n• Important information',
            whatsapp_summary='Test WhatsApp summary',
            auto_reply_text='Thank you for your email',
            status='processed'
        )
        db_session.add(email_log)
        
        # Add a notification
        notification = WhatsAppNotification(
            email_log_id=1,
            recipient='+1234567890',
            message='Test notification',
            status='sent'
        )
        db_session.add(notification)
        
        # Add a reply
        reply = EmailReply(
            email_log_id=1,
            reply_to='<EMAIL>',
            subject='Re: Test Email Subject',
            body='Thank you for your email',
            status='sent'
        )
        db_session.add(reply)
        
        db_session.commit()
        return email_log
    
    def test_root_endpoint(self, client):
        """Test the root endpoint."""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
    
    def test_get_emails_endpoint(self, client, sample_email_data):
        """Test getting emails via API."""
        response = client.get("/api/emails")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 1
        
        email = data[0]
        assert email["message_id"] == "test-message-123"
        assert email["sender"] == "John Doe <<EMAIL>>"
        assert email["subject"] == "Test Email Subject"
        assert email["status"] == "processed"
    
    def test_get_email_by_id_endpoint(self, client, sample_email_data):
        """Test getting a specific email by ID."""
        response = client.get("/api/emails/1")
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == 1
        assert data["message_id"] == "test-message-123"
        assert data["summary"] == "• Test email summary\n• Important information"
        assert data["whatsapp_summary"] == "Test WhatsApp summary"
        assert data["auto_reply_text"] == "Thank you for your email"
    
    def test_get_email_not_found(self, client):
        """Test getting a non-existent email."""
        response = client.get("/api/emails/999")
        assert response.status_code == 404
        
        data = response.json()
        assert "detail" in data
        assert "not found" in data["detail"].lower()
    
    def test_get_notifications_endpoint(self, client, sample_email_data):
        """Test getting notifications via API."""
        response = client.get("/api/notifications")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 1
        
        notification = data[0]
        assert notification["email_log_id"] == 1
        assert notification["recipient"] == "+1234567890"
        assert notification["status"] == "sent"
    
    def test_get_replies_endpoint(self, client, sample_email_data):
        """Test getting replies via API."""
        response = client.get("/api/replies")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 1
        
        reply = data[0]
        assert reply["email_log_id"] == 1
        assert reply["reply_to"] == "<EMAIL>"
        assert reply["status"] == "sent"
    
    def test_get_stats_endpoint(self, client, sample_email_data):
        """Test getting system statistics."""
        response = client.get("/api/stats")
        assert response.status_code == 200
        
        data = response.json()
        assert "emails" in data
        assert "notifications" in data
        assert "replies" in data
        
        # Check email stats
        assert data["emails"]["total"] == 1
        assert "processed" in data["emails"]["by_status"]
        assert data["emails"]["by_status"]["processed"] == 1
        
        # Check notification stats
        assert data["notifications"]["total"] == 1
        assert "sent" in data["notifications"]["by_status"]
        
        # Check reply stats
        assert data["replies"]["total"] == 1
        assert "sent" in data["replies"]["by_status"]
    
    def test_emails_filtering_by_status(self, client, sample_email_data):
        """Test filtering emails by status."""
        response = client.get("/api/emails?status=processed")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 1
        assert data[0]["status"] == "processed"
        
        # Test with non-existent status
        response = client.get("/api/emails?status=nonexistent")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 0
    
    def test_emails_filtering_by_sender(self, client, sample_email_data):
        """Test filtering emails by sender."""
        response = client.get("/api/emails?sender=John")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 1
        assert "John" in data[0]["sender"]
        
        # Test with non-existent sender
        response = client.get("/api/emails?sender=NonExistent")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 0
    
    def test_pagination(self, client, db_session):
        """Test pagination functionality."""
        # Create multiple emails
        for i in range(5):
            email = EmailLog(
                message_id=f'test-message-{i}',
                sender=f'Sender {i} <sender{i}@example.com>',
                recipient='<EMAIL>',
                subject=f'Test Subject {i}',
                status='received'
            )
            db_session.add(email)
        db_session.commit()
        
        # Test pagination
        response = client.get("/api/emails?skip=0&limit=3")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 3
        
        # Test second page
        response = client.get("/api/emails?skip=3&limit=3")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 2  # Remaining emails
    
    def test_notifications_filtering(self, client, sample_email_data):
        """Test filtering notifications."""
        response = client.get("/api/notifications?status=sent")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 1
        assert data[0]["status"] == "sent"
        
        response = client.get("/api/notifications?recipient=+1234567890")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 1
        assert data[0]["recipient"] == "+1234567890"
    
    def test_replies_filtering(self, client, sample_email_data):
        """Test filtering replies."""
        response = client.get("/api/replies?status=sent")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 1
        assert data[0]["status"] == "sent"
        
        response = client.get("/api/replies?reply_to=<EMAIL>")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 1
        assert data[0]["reply_to"] == "<EMAIL>"
