from pydantic import BaseModel, Field, EmailStr
from typing import List, Dict, Optional, Any, Union
from datetime import datetime

# Email Log Schemas
class EmailLogBase(BaseModel):
    message_id: str
    sender: str
    recipient: str
    subject: Optional[str] = None

class EmailLogCreate(EmailLogBase):
    pass

class EmailLogResponse(EmailLogBase):
    id: int
    received_at: datetime
    processed_at: Optional[datetime] = None
    status: str
    
    class Config:
        from_attributes = True

class EmailLogDetail(EmailLogResponse):
    summary: Optional[str] = None
    extracted_data: Optional[str] = None
    whatsapp_summary: Optional[str] = None
    auto_reply_text: Optional[str] = None
    error_message: Optional[str] = None
    notifications: List["NotificationResponse"] = []
    replies: List["ReplyResponse"] = []
    
    class Config:
        from_attributes = True

# WhatsApp Notification Schemas
class NotificationBase(BaseModel):
    recipient: str
    message: str

class NotificationCreate(NotificationBase):
    email_log_id: int

class NotificationResponse(NotificationBase):
    id: int
    email_log_id: int
    sent_at: Optional[datetime] = None
    status: str
    error_message: Optional[str] = None
    retry_count: int
    
    class Config:
        from_attributes = True

# Email Reply Schemas
class ReplyBase(BaseModel):
    reply_to: str
    subject: Optional[str] = None
    body: str

class ReplyCreate(ReplyBase):
    email_log_id: int

class ReplyResponse(ReplyBase):
    id: int
    email_log_id: int
    sent_at: Optional[datetime] = None
    status: str
    error_message: Optional[str] = None
    
    class Config:
        from_attributes = True

# Stats Response Schema
class StatusCount(BaseModel):
    total: int
    by_status: Dict[str, int]

class StatsResponse(BaseModel):
    emails: StatusCount
    notifications: StatusCount
    replies: StatusCount

# Update forward references
EmailLogDetail.update_forward_refs()
