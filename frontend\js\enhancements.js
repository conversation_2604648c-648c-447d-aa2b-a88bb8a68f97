/**
 * Enhanced UI Features for Email Monitor Agent Dashboard
 */

// Enhanced animations and interactions
const Enhancements = {
    // Initialize all enhancements
    init() {
        this.setupAnimatedCounters();
        this.setupParallaxEffects();
        this.setupKeyboardShortcuts();
        this.setupSearchFunctionality();
        this.setupRealTimeUpdates();
        this.setupProgressIndicators();
        console.log('🎨 UI Enhancements loaded');
    },

    // Animated counters for statistics
    setupAnimatedCounters() {
        const counters = document.querySelectorAll('.stat-content h3');
        
        const animateCounter = (element, target) => {
            const duration = 2000;
            const start = 0;
            const increment = target / (duration / 16);
            let current = start;
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current);
            }, 16);
        };

        // Intersection Observer for triggering animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const target = parseInt(entry.target.dataset.target) || 0;
                    animateCounter(entry.target, target);
                    observer.unobserve(entry.target);
                }
            });
        });

        counters.forEach(counter => observer.observe(counter));
    },

    // Parallax effects for cards
    setupParallaxEffects() {
        const cards = document.querySelectorAll('.stat-card, .dashboard-card');
        
        document.addEventListener('mousemove', (e) => {
            const { clientX, clientY } = e;
            const centerX = window.innerWidth / 2;
            const centerY = window.innerHeight / 2;
            
            cards.forEach(card => {
                const rect = card.getBoundingClientRect();
                const cardCenterX = rect.left + rect.width / 2;
                const cardCenterY = rect.top + rect.height / 2;
                
                const deltaX = (clientX - cardCenterX) * 0.01;
                const deltaY = (clientY - cardCenterY) * 0.01;
                
                card.style.transform = `perspective(1000px) rotateX(${deltaY}deg) rotateY(${deltaX}deg)`;
            });
        });

        // Reset on mouse leave
        document.addEventListener('mouseleave', () => {
            cards.forEach(card => {
                card.style.transform = '';
            });
        });
    },

    // Keyboard shortcuts
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.showSearchModal();
            }
            
            // Ctrl/Cmd + R for refresh
            if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                e.preventDefault();
                refreshDashboard();
            }
            
            // Number keys for tab navigation
            if (e.key >= '1' && e.key <= '4') {
                const tabs = ['dashboard', 'emails', 'employees', 'settings'];
                const tabIndex = parseInt(e.key) - 1;
                if (tabs[tabIndex]) {
                    App.switchTab(tabs[tabIndex]);
                }
            }
        });
    },

    // Enhanced search functionality
    setupSearchFunctionality() {
        // Create search modal
        const searchModal = document.createElement('div');
        searchModal.id = 'searchModal';
        searchModal.className = 'modal';
        searchModal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-search"></i> Quick Search</h3>
                    <button class="modal-close" onclick="this.closest('.modal').classList.remove('active')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <input type="text" id="searchInput" placeholder="Search emails, employees, or settings..." 
                           style="width: 100%; padding: 12px; border: 1px solid var(--border-color); border-radius: var(--radius-md);">
                    <div id="searchResults" style="margin-top: 1rem; max-height: 300px; overflow-y: auto;"></div>
                </div>
            </div>
        `;
        document.body.appendChild(searchModal);

        // Search functionality
        const searchInput = document.getElementById('searchInput');
        const searchResults = document.getElementById('searchResults');
        
        searchInput?.addEventListener('input', (e) => {
            const query = e.target.value.toLowerCase();
            if (query.length < 2) {
                searchResults.innerHTML = '';
                return;
            }
            
            // Mock search results
            const results = [
                { type: 'email', title: 'Recent email from client', action: () => App.switchTab('emails') },
                { type: 'employee', title: 'John Doe', action: () => App.switchTab('employees') },
                { type: 'setting', title: 'Email Configuration', action: () => App.switchTab('settings') }
            ].filter(item => item.title.toLowerCase().includes(query));
            
            searchResults.innerHTML = results.map(result => `
                <div class="search-result" onclick="${result.action}" style="padding: 8px; cursor: pointer; border-radius: 4px; margin-bottom: 4px;">
                    <i class="fas fa-${result.type === 'email' ? 'envelope' : result.type === 'employee' ? 'user' : 'cog'}"></i>
                    ${result.title}
                </div>
            `).join('');
        });
    },

    // Show search modal
    showSearchModal() {
        const modal = document.getElementById('searchModal');
        if (modal) {
            modal.classList.add('active');
            document.getElementById('searchInput')?.focus();
        }
    },

    // Real-time updates simulation
    setupRealTimeUpdates() {
        setInterval(() => {
            // Simulate real-time data updates
            const indicators = document.querySelectorAll('.status-indicator i');
            indicators.forEach(indicator => {
                indicator.style.animation = 'none';
                setTimeout(() => {
                    indicator.style.animation = 'statusPulse 2s infinite';
                }, 10);
            });
        }, 30000);
    },

    // Progress indicators
    setupProgressIndicators() {
        // Add progress bars to status charts
        const statusBars = document.querySelectorAll('.status-fill');
        statusBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 500);
        });
    },

    // Loading skeleton
    showSkeleton(container) {
        container.innerHTML = `
            <div class="skeleton skeleton-card"></div>
            <div class="skeleton skeleton-text long"></div>
            <div class="skeleton skeleton-text medium"></div>
            <div class="skeleton skeleton-text short"></div>
        `;
    },

    // Enhanced toast notifications
    showEnhancedToast(type, title, message, duration = 5000) {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div class="toast-header">
                <div class="toast-title">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    ${title}
                </div>
                <button class="toast-close" onclick="this.closest('.toast').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="toast-message">${message}</div>
            <div class="toast-progress" style="height: 3px; background: var(--${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'}-color); width: 100%; transition: width ${duration}ms linear;"></div>
        `;
        
        document.getElementById('toastContainer').appendChild(toast);
        
        // Auto remove
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => toast.remove(), 300);
        }, duration);
        
        // Progress bar animation
        setTimeout(() => {
            const progress = toast.querySelector('.toast-progress');
            if (progress) progress.style.width = '0%';
        }, 100);
    }
};

// Quick actions for FAB
function showQuickActions() {
    const actions = [
        { icon: 'fas fa-plus', text: 'Add Employee', action: () => openAddEmployeeModal() },
        { icon: 'fas fa-sync', text: 'Refresh Data', action: () => refreshDashboard() },
        { icon: 'fas fa-search', text: 'Search', action: () => Enhancements.showSearchModal() },
        { icon: 'fas fa-cog', text: 'Settings', action: () => App.switchTab('settings') }
    ];
    
    // Create quick actions menu
    const menu = document.createElement('div');
    menu.className = 'quick-actions-menu';
    menu.style.cssText = `
        position: fixed;
        bottom: 5rem;
        right: 2rem;
        background: white;
        border-radius: 12px;
        box-shadow: var(--shadow-lg);
        padding: 1rem;
        z-index: 1001;
        min-width: 200px;
    `;
    
    menu.innerHTML = actions.map(action => `
        <div class="quick-action" onclick="${action.action.toString().slice(6, -1)}; this.closest('.quick-actions-menu').remove();" 
             style="display: flex; align-items: center; gap: 12px; padding: 8px 12px; cursor: pointer; border-radius: 8px; transition: background 0.2s;">
            <i class="${action.icon}" style="color: var(--primary-color);"></i>
            <span>${action.text}</span>
        </div>
    `).join('');
    
    document.body.appendChild(menu);
    
    // Remove on click outside
    setTimeout(() => {
        document.addEventListener('click', function removeMenu(e) {
            if (!menu.contains(e.target) && !e.target.closest('.fab')) {
                menu.remove();
                document.removeEventListener('click', removeMenu);
            }
        });
    }, 100);
}

// Initialize enhancements when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    Enhancements.init();
});

// Export for global use
window.Enhancements = Enhancements;
