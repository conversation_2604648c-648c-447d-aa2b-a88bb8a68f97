# Email Monitor Agent - FastAPI

An AI-powered email monitoring and notification system built with FastAPI that automatically processes emails, generates summaries using AI, sends WhatsApp notifications via Meta Cloud API, and sends auto-replies.

## Features

- **Email Monitoring**: Monitors IMAP inbox for new emails
- **Sender Filtering**: Process emails only from specific sender addresses
- **AI Summarization**: Uses OpenAI GPT-4 or local LLM for email analysis
- **WhatsApp Notifications**: Sends notifications via Meta WhatsApp Cloud API
- **Auto-Reply**: Automatically responds to emails with AI-generated replies
- **FastAPI REST API**: Full REST API for monitoring and management
- **Database Logging**: Tracks all emails, notifications, and replies
- **Retry Logic**: Automatic retry for failed operations

## Project Structure

```
email_monitor_fastapi/
├── app/
│   ├── __init__.py
│   ├── models.py              # Database models
│   ├── schemas.py             # Pydantic schemas for API
│   ├── main.py               # FastAPI application with API endpoints
│   ├── worker.py             # Background worker and main application
│   ├── services/
│   │   ├── __init__.py
│   │   ├── email_monitor.py   # Email monitoring and IMAP handling
│   │   ├── ai_summarizer.py   # AI summarization (OpenAI/Local LLM)
│   │   ├── whatsapp_notifier.py # WhatsApp notifications (Meta Cloud API)
│   │   └── email_replier.py   # Email auto-reply functionality
│   └── utils/
│       ├── __init__.py
│       └── logging_utils.py   # Logging configuration and utilities
├── tests/
│   ├── __init__.py
│   ├── test_integration.py    # Integration tests
│   └── test_meta_api.py      # Meta WhatsApp API specific tests
├── .env.example              # Environment variables template
├── requirements.txt          # Python dependencies
└── README.md                # This file
```

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd email_monitor_fastapi
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your actual configuration
   ```

## Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

#### Email Monitoring (IMAP)
```env
IMAP_HOST=imap.gmail.com
IMAP_PORT=993
IMAP_USERNAME=<EMAIL>
IMAP_PASSWORD=your_app_password
IMAP_USE_SSL=True
IMAP_MAILBOX=INBOX
ALLOWED_SENDER_EMAIL=<EMAIL>  # Optional: filter by sender
```

#### AI Summarization
```env
USE_OPENAI=True
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
```

#### WhatsApp Notifications (Meta Cloud API)
```env
META_API_TOKEN=your_meta_access_token
META_PHONE_NUMBER_ID=your_whatsapp_business_phone_number_id
TEAM_NUMBERS=+**********,+**********
```

#### Email Auto-Reply (SMTP)
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
DEFAULT_SENDER=<EMAIL>
```

### Meta WhatsApp Cloud API Setup

1. **Create Meta Developer Account**: Go to [developers.facebook.com](https://developers.facebook.com)
2. **Create WhatsApp Business App**: Create a new app and add WhatsApp product
3. **Get Access Token**: Generate a permanent access token
4. **Get Phone Number ID**: Note your WhatsApp Business phone number ID
5. **Add Webhook** (optional): For receiving message status updates

## Usage

### Running the Application

1. **Start the FastAPI server**:
   ```bash
   cd email_monitor_fastapi
   python -m app.worker
   ```

2. **Access the API**:
   - API Documentation: http://localhost:8000/docs
   - API Base URL: http://localhost:8000

### API Endpoints

#### Email Management
- `GET /api/emails` - List all processed emails
- `GET /api/emails/{email_id}` - Get specific email details
- `POST /api/emails/{email_id}/process` - Process specific email

#### Notifications
- `GET /api/notifications` - List WhatsApp notifications
- `POST /api/notifications/retry` - Retry failed notifications

#### Replies
- `GET /api/replies` - List email replies
- `POST /api/replies/retry` - Retry failed replies

#### Monitoring
- `POST /api/monitor/start` - Start email monitoring
- `GET /api/stats` - Get system statistics

### Background Email Monitoring

The system automatically monitors emails in the background when started. The monitoring process:

1. **Connects to IMAP server** and checks for unread emails
2. **Filters emails** by sender (if configured)
3. **Processes emails** and stores in database
4. **AI Analysis** generates summaries and auto-reply text
5. **Sends WhatsApp notifications** to team members
6. **Sends auto-reply** to original sender
7. **Repeats** every 5 minutes (configurable)

## Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run specific test files
pytest tests/test_integration.py
pytest tests/test_meta_api.py

# Run with coverage
pytest --cov=app tests/
```

## Key Features

### Sender Filtering
Configure `ALLOWED_SENDER_EMAIL` to only process emails from specific senders:
```env
ALLOWED_SENDER_EMAIL=<EMAIL>
```

### Meta WhatsApp Cloud API
- Uses official Meta WhatsApp Cloud API (no Twilio dependency)
- Supports international phone number formats
- Automatic retry logic for failed messages
- Comprehensive error handling

### AI Summarization
- OpenAI GPT-4 integration for intelligent email analysis
- Generates concise summaries, WhatsApp notifications, and auto-replies
- Fallback to local LLM support
- Structured data extraction

### Database Tracking
- Complete audit trail of all emails, notifications, and replies
- Status tracking for all operations
- Retry counters and error logging
- SQLAlchemy ORM with SQLite (easily configurable for PostgreSQL/MySQL)

## Development

### Project Structure
- `app/models.py` - Database models and schemas
- `app/main.py` - FastAPI application and API routes
- `app/worker.py` - Background worker and main entry point
- `app/services/` - Core business logic services
- `app/utils/` - Utility functions and helpers
- `tests/` - Test suite

### Adding New Features
1. Add new service modules in `app/services/`
2. Update database models in `app/models.py`
3. Add API endpoints in `app/main.py`
4. Write tests in `tests/`

## Production Deployment

### Using Docker (Recommended)
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "-m", "app.worker"]
```

### Using Supervisor
```ini
[program:email_monitor]
command=python -m app.worker
directory=/path/to/email_monitor_fastapi
user=www-data
autostart=true
autorestart=true
```

## Troubleshooting

### Common Issues

1. **IMAP Connection Failed**
   - Check email credentials and app passwords
   - Verify IMAP is enabled for your email account
   - Check firewall and network connectivity

2. **WhatsApp API Errors**
   - Verify Meta access token is valid
   - Check phone number ID is correct
   - Ensure phone numbers are in international format

3. **OpenAI API Issues**
   - Verify API key is valid and has credits
   - Check rate limits and quotas
   - Monitor API usage in OpenAI dashboard

## License

[Add your license information here]

## Contributing

[Add contribution guidelines here]
