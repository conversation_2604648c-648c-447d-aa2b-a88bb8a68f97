/**
 * Email management functionality for Email Monitor Agent
 */

// Extend the App object with email methods
Object.assign(App, {
    // Current email filters
    emailFilters: {
        date: 'all',
        sender: '',
        status: 'all',
        page: 1
    },
    
    // Load emails data
    async loadEmailsData() {
        try {
            this.showLoading();
            
            // Build query parameters
            const params = new URLSearchParams();
            
            if (this.emailFilters.sender) {
                params.append('sender', this.emailFilters.sender);
            }
            
            if (this.emailFilters.status !== 'all') {
                params.append('status', this.emailFilters.status);
            }
            
            // Date filtering
            if (this.emailFilters.date !== 'all') {
                const dateRange = this.getDateRange(this.emailFilters.date);
                if (dateRange.start) {
                    params.append('start_date', dateRange.start);
                }
                if (dateRange.end) {
                    params.append('end_date', dateRange.end);
                }
            }
            
            // Pagination
            const skip = (this.emailFilters.page - 1) * this.config.itemsPerPage;
            params.append('skip', skip);
            params.append('limit', this.config.itemsPerPage);
            
            const response = await this.apiCall(`/api/emails?${params.toString()}`);
            
            if (response.ok) {
                const emails = await response.json();
                this.renderEmailsList(emails);
                this.updateEmailFiltersFromUI();
                this.cache.emails = emails;
            }
        } catch (error) {
            console.error('Failed to load emails:', error);
            this.showToast('error', 'Error', 'Failed to load emails');
        } finally {
            this.hideLoading();
        }
    },
    
    // Get date range for filtering
    getDateRange(period) {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        
        switch (period) {
            case 'today':
                return {
                    start: today.toISOString(),
                    end: new Date(today.getTime() + 24 * 60 * 60 * 1000).toISOString()
                };
            case 'week':
                const weekStart = new Date(today);
                weekStart.setDate(today.getDate() - today.getDay());
                return {
                    start: weekStart.toISOString(),
                    end: now.toISOString()
                };
            case 'month':
                const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                return {
                    start: monthStart.toISOString(),
                    end: now.toISOString()
                };
            default:
                return { start: null, end: null };
        }
    },
    
    // Update filters from UI
    updateEmailFiltersFromUI() {
        this.emailFilters.date = document.getElementById('dateFilter').value;
        this.emailFilters.sender = document.getElementById('senderFilter').value;
        this.emailFilters.status = document.getElementById('statusFilter').value;
    },
    
    // Render emails list
    renderEmailsList(emails) {
        const container = document.getElementById('emailsList');
        
        if (!emails || emails.length === 0) {
            container.innerHTML = `
                <div class="email-item">
                    <div class="email-content">
                        <div class="email-header">
                            <div class="email-sender">No emails found</div>
                        </div>
                        <div class="email-subject">No emails match your current filters</div>
                        <div class="email-summary">Try adjusting your filters or check back later for new emails.</div>
                    </div>
                </div>
            `;
            return;
        }
        
        const emailsHtml = emails.map(email => {
            const statusClass = this.getEmailStatusClass(email.status);
            const hasWhatsApp = email.whatsapp_notifications && email.whatsapp_notifications.length > 0;
            const hasReply = email.email_replies && email.email_replies.length > 0;
            
            return `
                <div class="email-item" onclick="showEmailDetails(${email.id})">
                    <div class="email-status ${statusClass}"></div>
                    <div class="email-content">
                        <div class="email-header">
                            <div class="email-sender">${this.extractEmailFromSender(email.sender)}</div>
                            <div class="email-time">${this.formatDate(email.received_at)}</div>
                        </div>
                        <div class="email-subject">${email.subject || 'No Subject'}</div>
                        <div class="email-summary">${this.truncateText(email.summary || 'No summary available', 150)}</div>
                    </div>
                    <div class="email-actions">
                        ${hasWhatsApp ? '<span class="email-badge whatsapp"><i class="fab fa-whatsapp"></i> Sent</span>' : ''}
                        ${hasReply ? '<span class="email-badge reply"><i class="fas fa-reply"></i> Replied</span>' : ''}
                        <span class="email-badge ${statusClass}">${email.status}</span>
                    </div>
                </div>
            `;
        }).join('');
        
        container.innerHTML = emailsHtml;
        
        // Update pagination (simplified for now)
        this.updateEmailsPagination(emails.length);
    },
    
    // Get CSS class for email status
    getEmailStatusClass(status) {
        const statusMap = {
            'processed': 'processed',
            'pending': 'pending',
            'failed': 'failed',
            'received': 'pending'
        };
        return statusMap[status] || 'pending';
    },
    
    // Update pagination
    updateEmailsPagination(emailCount) {
        const container = document.getElementById('emailsPagination');
        const currentPage = this.emailFilters.page;
        const hasMore = emailCount === this.config.itemsPerPage;
        
        let paginationHtml = '';
        
        // Previous button
        if (currentPage > 1) {
            paginationHtml += `
                <button class="pagination-btn" onclick="App.changePage(${currentPage - 1})">
                    <i class="fas fa-chevron-left"></i> Previous
                </button>
            `;
        }
        
        // Current page
        paginationHtml += `
            <span class="pagination-btn active">Page ${currentPage}</span>
        `;
        
        // Next button
        if (hasMore) {
            paginationHtml += `
                <button class="pagination-btn" onclick="App.changePage(${currentPage + 1})">
                    Next <i class="fas fa-chevron-right"></i>
                </button>
            `;
        }
        
        container.innerHTML = paginationHtml;
    },
    
    // Change page
    changePage(page) {
        this.emailFilters.page = page;
        this.loadEmailsData();
    },
    
    // Show email details in modal
    async showEmailDetails(emailId) {
        try {
            this.showLoading();
            
            const response = await this.apiCall(`/api/emails/${emailId}`);
            
            if (response.ok) {
                const email = await response.json();
                this.renderEmailModal(email);
                document.getElementById('emailModal').classList.add('active');
            } else {
                this.showToast('error', 'Error', 'Failed to load email details');
            }
        } catch (error) {
            console.error('Failed to load email details:', error);
            this.showToast('error', 'Error', 'Failed to load email details');
        } finally {
            this.hideLoading();
        }
    },
    
    // Render email details modal
    renderEmailModal(email) {
        const container = document.getElementById('emailModalContent');
        
        const whatsappNotifications = email.whatsapp_notifications || [];
        const emailReplies = email.email_replies || [];
        
        container.innerHTML = `
            <div class="email-detail">
                <div class="email-detail-header">
                    <h4>${email.subject || 'No Subject'}</h4>
                    <span class="email-badge ${this.getEmailStatusClass(email.status)}">${email.status}</span>
                </div>
                
                <div class="email-detail-meta">
                    <div class="meta-item">
                        <strong>From:</strong> ${email.sender}
                    </div>
                    <div class="meta-item">
                        <strong>To:</strong> ${email.recipient}
                    </div>
                    <div class="meta-item">
                        <strong>Received:</strong> ${this.formatDate(email.received_at)}
                    </div>
                    <div class="meta-item">
                        <strong>Message ID:</strong> ${email.message_id}
                    </div>
                </div>
                
                ${email.summary ? `
                    <div class="email-detail-section">
                        <h5><i class="fas fa-list"></i> AI Summary</h5>
                        <div class="summary-content">${email.summary.replace(/\n/g, '<br>')}</div>
                    </div>
                ` : ''}
                
                ${email.whatsapp_summary ? `
                    <div class="email-detail-section">
                        <h5><i class="fab fa-whatsapp"></i> WhatsApp Message</h5>
                        <div class="whatsapp-content">${email.whatsapp_summary}</div>
                    </div>
                ` : ''}
                
                ${email.auto_reply_text ? `
                    <div class="email-detail-section">
                        <h5><i class="fas fa-reply"></i> Auto-Reply</h5>
                        <div class="reply-content">${email.auto_reply_text}</div>
                    </div>
                ` : ''}
                
                ${whatsappNotifications.length > 0 ? `
                    <div class="email-detail-section">
                        <h5><i class="fab fa-whatsapp"></i> WhatsApp Notifications (${whatsappNotifications.length})</h5>
                        <div class="notifications-list">
                            ${whatsappNotifications.map(notif => `
                                <div class="notification-item">
                                    <span class="notification-recipient">${notif.recipient}</span>
                                    <span class="notification-status ${notif.status}">${notif.status}</span>
                                    <span class="notification-time">${this.formatDate(notif.sent_at)}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
                
                ${emailReplies.length > 0 ? `
                    <div class="email-detail-section">
                        <h5><i class="fas fa-reply"></i> Email Replies (${emailReplies.length})</h5>
                        <div class="replies-list">
                            ${emailReplies.map(reply => `
                                <div class="reply-item">
                                    <span class="reply-to">${reply.reply_to}</span>
                                    <span class="reply-status ${reply.status}">${reply.status}</span>
                                    <span class="reply-time">${this.formatDate(reply.sent_at)}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }
});

// Add CSS for email details modal
const emailModalStyles = `
<style>
.email-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.email-detail-meta {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-md);
}

.meta-item {
    font-size: 0.875rem;
}

.email-detail-section {
    margin-bottom: var(--spacing-lg);
}

.email-detail-section h5 {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: 600;
}

.summary-content, .whatsapp-content, .reply-content {
    padding: var(--spacing-md);
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-md);
    line-height: 1.6;
}

.notifications-list, .replies-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.notification-item, .reply-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
}

.notification-status, .reply-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.notification-status.sent, .reply-status.sent {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.notification-status.failed, .reply-status.failed {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}
</style>
`;

// Add the styles to the document head
document.head.insertAdjacentHTML('beforeend', emailModalStyles);
