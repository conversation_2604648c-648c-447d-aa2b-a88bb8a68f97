#!/usr/bin/env python3
"""
Improved Email Monitoring Background Process

This script runs the email monitoring with better error handling and logging.
"""

import os
import sys
import time
import logging
import traceback
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add app to path
sys.path.append('.')

from app.models import get_db
from app.services.email_monitor import EmailMonitor, EmailProcessor
from app.services.ai_summarizer import get_ai_summarizer
from app.services.whatsapp_notifier import get_whatsapp_notifier
from app.services.email_replier import get_email_replier

def get_config():
    """Get application configuration from environment variables"""
    return {
        # Email monitoring (IMAP)
        "imap_host": os.getenv("IMAP_HOST"),
        "imap_port": int(os.getenv("IMAP_PORT", "993")),
        "imap_username": os.getenv("IMAP_USERNAME"),
        "imap_password": os.getenv("IMAP_PASSWORD"),
        "use_ssl": os.getenv("IMAP_USE_SSL", "True").lower() == "true",
        "mailbox": os.getenv("IMAP_MAILBOX", "INBOX"),
        "allowed_sender_email": os.getenv("ALLOWED_SENDER_EMAIL"),
        
        # AI summarization
        "use_openai": os.getenv("USE_OPENAI", "True").lower() == "true",
        "openai_api_key": os.getenv("OPENAI_API_KEY"),
        "openai_model": os.getenv("OPENAI_MODEL", "gpt-4"),
        
        # WhatsApp notification (Meta Cloud API)
        "mock_whatsapp": os.getenv("MOCK_WHATSAPP", "False").lower() == "true",
        "meta_api_token": os.getenv("META_API_TOKEN"),
        "meta_phone_number_id": os.getenv("META_PHONE_NUMBER_ID"),
        "team_numbers": os.getenv("TEAM_NUMBERS", "").split(","),
        
        # Email reply (SMTP)
        "smtp_host": os.getenv("SMTP_HOST"),
        "smtp_port": int(os.getenv("SMTP_PORT", "587")),
        "smtp_username": os.getenv("SMTP_USERNAME"),
        "smtp_password": os.getenv("SMTP_PASSWORD"),
        "smtp_use_ssl": os.getenv("SMTP_USE_SSL", "False").lower() == "true",
        "default_sender": os.getenv("DEFAULT_SENDER"),
        "mock_email": os.getenv("MOCK_EMAIL", "False").lower() == "true",
        
        # Polling interval
        "polling_interval": int(os.getenv("POLLING_INTERVAL", "300")),  # 5 minutes
    }

def monitor_emails_improved(config, db):
    """Improved email monitoring with detailed logging."""
    try:
        print(f"🔌 Connecting to {config['imap_host']}...")
        
        # Create email monitor
        monitor = EmailMonitor(
            host=config['imap_host'],
            username=config['imap_username'],
            password=config['imap_password'],
            port=config['imap_port'],
            use_ssl=config['use_ssl'],
            mailbox=config['mailbox']
        )
        
        # Connect and select mailbox
        if not monitor.connect():
            print("❌ Failed to connect to email server")
            return {"success": False, "error": "Connection failed"}
        
        if not monitor.select_mailbox():
            print("❌ Failed to select mailbox")
            monitor.disconnect()
            return {"success": False, "error": "Mailbox selection failed"}
        
        print("✅ Connected to email server")
        
        # Search for unread emails first, then recent emails
        unread_emails = monitor.search_emails('UNSEEN')
        all_emails = monitor.search_emails('ALL')
        
        # Get recent emails (last 5) if no unread emails
        emails_to_check = unread_emails if unread_emails else (all_emails[-5:] if len(all_emails) >= 5 else all_emails)
        
        print(f"📊 Total emails: {len(all_emails)}, Unread: {len(unread_emails)}, Checking: {len(emails_to_check)}")
        
        # Create processor
        processor = EmailProcessor(db, config['allowed_sender_email'])
        
        # Process emails
        new_emails = 0
        processed_emails = 0
        notifications_sent = 0
        replies_sent = 0
        
        for email_id in emails_to_check:
            try:
                # Fetch email
                email_data = monitor.fetch_email(email_id)
                if not email_data:
                    continue
                
                print(f"📧 From: {email_data['sender']['email']} | Subject: {email_data['subject'][:30]}...")
                
                # Check if already processed
                from app.models import EmailLog
                existing = db.query(EmailLog).filter_by(message_id=email_data['message_id']).first()
                if existing:
                    print(f"   ♻️ Already processed")
                    continue
                
                new_emails += 1
                
                # Check sender filtering
                if config['allowed_sender_email'] and email_data['sender']['email'] != config['allowed_sender_email']:
                    print(f"   🔍 Filtered out (not from allowed sender)")
                    continue
                
                print(f"   ✅ Processing email from allowed sender...")
                
                # Process email
                email_log = processor.process_email(email_data)
                if not email_log:
                    print(f"   ❌ Failed to store email")
                    continue
                
                processed_emails += 1
                print(f"   ✅ Stored in database (ID: {email_log.id})")
                
                # AI Processing
                try:
                    print(f"   🤖 AI processing...")
                    ai_summarizer = get_ai_summarizer(config)
                    email_content = email_data['body']['text'] or email_data['body']['html'] or "No content"
                    ai_result = ai_summarizer.process_email(email_log, email_content, db)
                    
                    if ai_result.get('success'):
                        print(f"   ✅ AI processing completed")
                    else:
                        print(f"   ⚠️ AI processing had issues")
                except Exception as e:
                    print(f"   ❌ AI processing error: {str(e)}")
                
                # WhatsApp Notification
                try:
                    print(f"   📱 Sending WhatsApp notification...")
                    whatsapp_notifier = get_whatsapp_notifier(config)
                    notifications = whatsapp_notifier.send_notification(email_log, db)
                    
                    if notifications:
                        notifications_sent += len(notifications)
                        print(f"   ✅ WhatsApp sent to {len(notifications)} recipients")
                        for notif in notifications:
                            print(f"      📱 {notif.recipient}: {notif.status}")
                    else:
                        print(f"   ⚠️ No WhatsApp notifications sent")
                except Exception as e:
                    print(f"   ❌ WhatsApp error: {str(e)}")
                
                # Auto-Reply
                try:
                    print(f"   📤 Sending auto-reply...")
                    email_replier = get_email_replier(config)
                    reply = email_replier.send_reply(email_log, db)
                    
                    if reply and reply.status == 'sent':
                        replies_sent += 1
                        print(f"   ✅ Auto-reply sent to {reply.reply_to}")
                    else:
                        print(f"   ⚠️ Auto-reply not sent")
                except Exception as e:
                    print(f"   ❌ Auto-reply error: {str(e)}")
                
            except Exception as e:
                print(f"   ❌ Error processing email {email_id}: {str(e)}")
                continue
        
        monitor.disconnect()
        
        # Summary
        result = {
            "success": True,
            "total_checked": len(emails_to_check),
            "new_emails": new_emails,
            "processed_emails": processed_emails,
            "notifications_sent": notifications_sent,
            "replies_sent": replies_sent
        }
        
        print(f"📊 Summary: {processed_emails} processed, {notifications_sent} WhatsApp sent, {replies_sent} replies sent")
        return result
        
    except Exception as e:
        print(f"❌ Monitoring error: {str(e)}")
        traceback.print_exc()
        return {"success": False, "error": str(e)}

def main():
    """Main monitoring loop with improved error handling."""
    print("🚀 Starting Improved Email Monitor Background Process...")
    print("=" * 60)
    
    config = get_config()
    
    print(f"📧 Monitoring: {config['imap_username']}")
    print(f"🔍 Allowed sender: {config['allowed_sender_email']}")
    print(f"⏰ Check interval: {config['polling_interval']} seconds")
    print(f"📱 WhatsApp notifications: {len(config['team_numbers'])} recipients")
    print("=" * 60)
    
    cycle_count = 0
    
    while True:
        try:
            cycle_count += 1
            print(f"\n🔄 Email monitoring cycle #{cycle_count} - {time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Get database session
            db = get_db()
            
            # Monitor emails with improved function
            result = monitor_emails_improved(config, db)
            
            if result.get("success"):
                if result.get('processed_emails', 0) > 0:
                    print(f"🎉 SUCCESS: Processed {result['processed_emails']} emails!")
                    print(f"   📱 WhatsApp notifications: {result['notifications_sent']}")
                    print(f"   📤 Auto-replies: {result['replies_sent']}")
                else:
                    print(f"✅ No new emails to process")
            else:
                print(f"⚠️ Monitoring cycle had issues: {result.get('error', 'Unknown error')}")
            
            db.close()
            
            print(f"😴 Sleeping for {config['polling_interval']} seconds...")
            time.sleep(config["polling_interval"])
            
        except KeyboardInterrupt:
            print(f"\n🛑 Email monitoring stopped by user")
            break
        except Exception as e:
            print(f"❌ Critical error in monitoring cycle: {str(e)}")
            traceback.print_exc()
            print(f"🔄 Retrying in 60 seconds...")
            time.sleep(60)

if __name__ == "__main__":
    main()
