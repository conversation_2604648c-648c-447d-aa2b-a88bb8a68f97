import pytest
import json
from unittest.mock import Mock, patch
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.models import Base, EmailLog, WhatsAppNotification
from app.services.whatsapp_notifier import WhatsAppNotifier

class TestMetaWhatsAppAPI:
    """Tests specifically for Meta WhatsApp Cloud API integration."""
    
    @pytest.fixture
    def db_session(self):
        """Create a test database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        session = SessionLocal()
        
        yield session
        
        session.close()
    
    @pytest.fixture
    def sample_email_log(self, db_session):
        """Create a sample email log for testing."""
        email_log = EmailLog(
            message_id='test-message-123',
            sender='<PERSON> <<EMAIL>>',
            recipient='Test Recipient <<EMAIL>>',
            subject='Test Email Subject',
            whatsapp_summary='Test WhatsApp summary for notification',
            status='processed'
        )
        db_session.add(email_log)
        db_session.commit()
        return email_log
    
    @pytest.fixture
    def whatsapp_notifier(self):
        """Create a WhatsApp notifier instance for testing."""
        return WhatsAppNotifier(
            access_token='test-access-token',
            phone_number_id='test-phone-number-id',
            team_numbers=['+1234567890', '+0987654321'],
            api_version='v18.0'
        )
    
    def test_whatsapp_notifier_initialization(self, whatsapp_notifier):
        """Test WhatsApp notifier initialization."""
        assert whatsapp_notifier.access_token == 'test-access-token'
        assert whatsapp_notifier.phone_number_id == 'test-phone-number-id'
        assert whatsapp_notifier.api_version == 'v18.0'
        assert whatsapp_notifier.base_url == 'https://graph.facebook.com/v18.0/test-phone-number-id/messages'
        assert len(whatsapp_notifier.team_numbers) == 2
        assert '+1234567890' in whatsapp_notifier.team_numbers
        assert '+0987654321' in whatsapp_notifier.team_numbers
    
    def test_phone_number_formatting(self):
        """Test phone number formatting in notifier initialization."""
        # Test with various phone number formats
        notifier = WhatsAppNotifier(
            access_token='test-token',
            phone_number_id='test-id',
            team_numbers=['1234567890', '+0987654321', 'whatsapp:+1122334455'],
            api_version='v18.0'
        )
        
        expected_numbers = ['+1234567890', '+0987654321', '+1122334455']
        assert notifier.team_numbers == expected_numbers
    
    @patch('app.services.whatsapp_notifier.requests.post')
    def test_successful_notification_send(self, mock_post, whatsapp_notifier, db_session, sample_email_log):
        """Test successful WhatsApp notification sending."""
        # Mock successful API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'messages': [{'id': 'wamid.test-message-id'}]
        }
        mock_post.return_value = mock_response
        
        # Send notification
        notifications = whatsapp_notifier.send_notification(sample_email_log, db_session)
        
        # Verify notifications were created
        assert len(notifications) == 2  # Two team members
        
        for notification in notifications:
            assert notification.status == 'sent'
            assert notification.email_log_id == sample_email_log.id
            assert notification.recipient in ['+1234567890', '+0987654321']
            assert notification.sent_at is not None
        
        # Verify API calls were made
        assert mock_post.call_count == 2
        
        # Verify API call parameters
        call_args = mock_post.call_args_list[0]
        assert call_args[1]['headers']['Authorization'] == 'Bearer test-access-token'
        assert call_args[1]['headers']['Content-Type'] == 'application/json'
        
        # Verify payload structure
        payload = call_args[1]['json']
        assert payload['messaging_product'] == 'whatsapp'
        assert payload['type'] == 'text'
        assert 'to' in payload
        assert 'text' in payload
        assert 'body' in payload['text']
    
    @patch('app.services.whatsapp_notifier.requests.post')
    def test_api_error_handling(self, mock_post, whatsapp_notifier, db_session, sample_email_log):
        """Test handling of Meta API errors."""
        # Mock API error response
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = '{"error": {"message": "Invalid phone number"}}'
        mock_post.return_value = mock_response
        
        # Send notification
        notifications = whatsapp_notifier.send_notification(sample_email_log, db_session)
        
        # Verify notifications were created but marked as failed
        assert len(notifications) == 0  # No successful notifications
        
        # Check database for failed notifications
        failed_notifications = db_session.query(WhatsAppNotification).filter_by(status='failed').all()
        assert len(failed_notifications) == 2
        
        for notification in failed_notifications:
            assert notification.status == 'failed'
            assert 'Meta API error: 400' in notification.error_message
            assert notification.retry_count == 1
    
    @patch('app.services.whatsapp_notifier.requests.post')
    def test_network_error_handling(self, mock_post, whatsapp_notifier, db_session, sample_email_log):
        """Test handling of network errors."""
        # Mock network error
        mock_post.side_effect = Exception("Network connection failed")
        
        # Send notification
        notifications = whatsapp_notifier.send_notification(sample_email_log, db_session)
        
        # Verify notifications were created but marked as failed
        assert len(notifications) == 0
        
        # Check database for failed notifications
        failed_notifications = db_session.query(WhatsAppNotification).filter_by(status='failed').all()
        assert len(failed_notifications) == 2
        
        for notification in failed_notifications:
            assert notification.status == 'failed'
            assert 'Error: Network connection failed' in notification.error_message
    
    def test_message_formatting(self, whatsapp_notifier, sample_email_log):
        """Test WhatsApp message formatting."""
        formatted_message = whatsapp_notifier._format_message(sample_email_log)
        
        assert '📧 *New Email Summary*' in formatted_message
        assert '*From:* John Doe' in formatted_message
        assert '*Subject:* Test Email Subject' in formatted_message
        assert 'Test WhatsApp summary for notification' in formatted_message
    
    @patch('app.services.whatsapp_notifier.requests.post')
    def test_retry_failed_notifications(self, mock_post, whatsapp_notifier, db_session, sample_email_log):
        """Test retrying failed notifications."""
        # First, create some failed notifications
        failed_notification = WhatsAppNotification(
            email_log_id=sample_email_log.id,
            recipient='+1234567890',
            message='Test message',
            status='failed',
            error_message='Previous error',
            retry_count=0
        )
        db_session.add(failed_notification)
        db_session.commit()
        
        # Mock successful retry
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'messages': [{'id': 'wamid.retry-message-id'}]
        }
        mock_post.return_value = mock_response
        
        # Retry failed notifications
        success_count = whatsapp_notifier.retry_failed_notifications(max_retries=3, db_session=db_session)
        
        # Verify retry was successful
        assert success_count == 1
        
        # Verify notification status was updated
        db_session.refresh(failed_notification)
        assert failed_notification.status == 'sent'
        assert failed_notification.retry_count == 1
        assert failed_notification.sent_at is not None
    
    @patch('app.services.whatsapp_notifier.requests.post')
    def test_max_retries_exceeded(self, mock_post, whatsapp_notifier, db_session, sample_email_log):
        """Test behavior when max retries are exceeded."""
        # Create a notification that has already exceeded max retries
        failed_notification = WhatsAppNotification(
            email_log_id=sample_email_log.id,
            recipient='+1234567890',
            message='Test message',
            status='failed',
            error_message='Previous error',
            retry_count=3  # Already at max retries
        )
        db_session.add(failed_notification)
        db_session.commit()
        
        # Attempt retry
        success_count = whatsapp_notifier.retry_failed_notifications(max_retries=3, db_session=db_session)
        
        # Verify no retries were attempted
        assert success_count == 0
        assert mock_post.call_count == 0
    
    def test_missing_whatsapp_summary(self, whatsapp_notifier, db_session):
        """Test handling when email has no WhatsApp summary."""
        # Create email log without WhatsApp summary
        email_log = EmailLog(
            message_id='test-no-summary',
            sender='Test Sender <<EMAIL>>',
            recipient='Test Recipient <<EMAIL>>',
            subject='Test Subject',
            whatsapp_summary=None,  # No summary
            status='processed'
        )
        db_session.add(email_log)
        db_session.commit()
        
        # Attempt to send notification
        notifications = whatsapp_notifier.send_notification(email_log, db_session)
        
        # Verify no notifications were sent
        assert len(notifications) == 0
    
    def test_api_version_configuration(self):
        """Test different API version configurations."""
        # Test default version
        notifier_default = WhatsAppNotifier(
            access_token='test-token',
            phone_number_id='test-id',
            team_numbers=['+1234567890']
        )
        assert 'v18.0' in notifier_default.base_url
        
        # Test custom version
        notifier_custom = WhatsAppNotifier(
            access_token='test-token',
            phone_number_id='test-id',
            team_numbers=['+1234567890'],
            api_version='v17.0'
        )
        assert 'v17.0' in notifier_custom.base_url
