/**
 * Dashboard functionality for Email Monitor Agent
 */

// Extend the App object with dashboard methods
Object.assign(App, {
    // Load dashboard data
    async loadDashboardData() {
        try {
            this.showLoading();
            
            // Load stats and recent activity in parallel
            const [statsResponse, emailsResponse] = await Promise.all([
                this.apiCall('/api/stats'),
                this.apiCall('/api/emails?limit=5')
            ]);
            
            if (statsResponse.ok && emailsResponse.ok) {
                const stats = await statsResponse.json();
                const emails = await emailsResponse.json();
                
                this.updateStatsCards(stats);
                this.updateStatusChart(stats);
                this.updateRecentActivity(emails);
                
                this.cache.stats = stats;
                this.cache.lastUpdate = new Date();
            }
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            this.showToast('error', 'Error', 'Failed to load dashboard data');
        } finally {
            this.hideLoading();
        }
    },
    
    // Update statistics cards
    updateStatsCards(stats) {
        // Total emails
        const totalEmails = stats.emails?.total || 0;
        document.getElementById('totalEmails').textContent = totalEmails;
        document.getElementById('emailsChange').textContent = `+${stats.emails?.today || 0} today`;
        
        // WhatsApp notifications
        const totalNotifications = stats.notifications?.total || 0;
        document.getElementById('totalNotifications').textContent = totalNotifications;
        document.getElementById('notificationsChange').textContent = `+${stats.notifications?.today || 0} today`;
        
        // Auto replies
        const totalReplies = stats.replies?.total || 0;
        document.getElementById('totalReplies').textContent = totalReplies;
        document.getElementById('repliesChange').textContent = `+${stats.replies?.today || 0} today`;
        
        // Employees (this would come from a separate endpoint)
        this.updateEmployeeCount();
    },
    
    // Update employee count
    async updateEmployeeCount() {
        try {
            const response = await this.apiCall('/api/employees');
            if (response.ok) {
                const employees = await response.json();
                document.getElementById('totalEmployees').textContent = employees.length;
                document.getElementById('employeesChange').textContent = 'Active';
            }
        } catch (error) {
            document.getElementById('totalEmployees').textContent = '0';
            document.getElementById('employeesChange').textContent = 'Error';
        }
    },
    
    // Update status chart
    updateStatusChart(stats) {
        const emailStats = stats.emails?.by_status || {};
        const total = stats.emails?.total || 1; // Avoid division by zero
        
        // Calculate percentages
        const processed = emailStats.processed || 0;
        const pending = emailStats.pending || 0;
        const failed = emailStats.failed || 0;
        
        const processedPercent = (processed / total) * 100;
        const pendingPercent = (pending / total) * 100;
        const failedPercent = (failed / total) * 100;
        
        // Update progress bars
        document.getElementById('processedBar').style.width = `${processedPercent}%`;
        document.getElementById('pendingBar').style.width = `${pendingPercent}%`;
        document.getElementById('failedBar').style.width = `${failedPercent}%`;
        
        // Update counts
        document.getElementById('processedCount').textContent = processed;
        document.getElementById('pendingCount').textContent = pending;
        document.getElementById('failedCount').textContent = failed;
    },
    
    // Update recent activity
    updateRecentActivity(emails) {
        const container = document.getElementById('recentEmails');
        
        if (!emails || emails.length === 0) {
            container.innerHTML = `
                <div class="activity-item">
                    <div class="activity-icon email">
                        <i class="fas fa-inbox"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">No recent emails</div>
                        <div class="activity-description">No emails have been processed yet</div>
                    </div>
                    <div class="activity-time">-</div>
                </div>
            `;
            return;
        }
        
        const activityHtml = emails.map(email => {
            const hasWhatsApp = email.whatsapp_notifications && email.whatsapp_notifications.length > 0;
            const hasReply = email.email_replies && email.email_replies.length > 0;
            
            return `
                <div class="activity-item" onclick="showEmailDetails(${email.id})">
                    <div class="activity-icon email">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">${this.truncateText(email.subject, 50)}</div>
                        <div class="activity-description">
                            From: ${this.extractEmailFromSender(email.sender)}
                            ${hasWhatsApp ? '<span class="email-badge whatsapp"><i class="fab fa-whatsapp"></i> Sent</span>' : ''}
                            ${hasReply ? '<span class="email-badge reply"><i class="fas fa-reply"></i> Replied</span>' : ''}
                        </div>
                    </div>
                    <div class="activity-time">${this.formatDate(email.received_at)}</div>
                </div>
            `;
        }).join('');
        
        container.innerHTML = activityHtml;
    },
    
    // Extract email address from sender string
    extractEmailFromSender(sender) {
        const match = sender.match(/<(.+?)>/);
        return match ? match[1] : sender;
    },
    
    // Generate sample activity data (for demo purposes)
    generateSampleActivity() {
        return [
            {
                type: 'email',
                title: 'New email processed',
                description: 'From: <EMAIL> - Project Update Required',
                time: new Date(Date.now() - 5 * 60000), // 5 minutes ago
                badges: ['whatsapp', 'reply']
            },
            {
                type: 'whatsapp',
                title: 'WhatsApp notification sent',
                description: 'Sent to 3 team members',
                time: new Date(Date.now() - 15 * 60000), // 15 minutes ago
                badges: []
            },
            {
                type: 'reply',
                title: 'Auto-reply sent',
                description: 'To: <EMAIL>',
                time: new Date(Date.now() - 20 * 60000), // 20 minutes ago
                badges: []
            }
        ];
    },
    
    // Render activity items
    renderActivityItems(activities) {
        return activities.map(activity => {
            const badges = activity.badges.map(badge => {
                const badgeConfig = {
                    whatsapp: { icon: 'fab fa-whatsapp', text: 'WhatsApp' },
                    reply: { icon: 'fas fa-reply', text: 'Reply' }
                };
                
                const config = badgeConfig[badge];
                return config ? `<span class="email-badge ${badge}"><i class="${config.icon}"></i> ${config.text}</span>` : '';
            }).join('');
            
            return `
                <div class="activity-item">
                    <div class="activity-icon ${activity.type}">
                        <i class="fas fa-${this.getActivityIcon(activity.type)}"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">${activity.title}</div>
                        <div class="activity-description">
                            ${activity.description}
                            ${badges}
                        </div>
                    </div>
                    <div class="activity-time">${this.formatDate(activity.time)}</div>
                </div>
            `;
        }).join('');
    },
    
    // Get icon for activity type
    getActivityIcon(type) {
        const icons = {
            email: 'envelope',
            whatsapp: 'comment',
            reply: 'reply'
        };
        return icons[type] || 'circle';
    },
    
    // Refresh dashboard data
    refreshDashboard() {
        this.loadDashboardData();
        this.showToast('info', 'Refreshed', 'Dashboard data has been updated');
    }
});

// Global function to show email details
function showEmailDetails(emailId) {
    App.showEmailDetails(emailId);
}
