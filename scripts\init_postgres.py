#!/usr/bin/env python3
"""
PostgreSQL Database Initialization Script

This script creates the PostgreSQL database and tables for the Email Monitor Agent.
Run this script before starting the application for the first time.
"""

import os
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from dotenv import load_dotenv

# Add the parent directory to the path so we can import our models
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.models import Base, get_db
from sqlalchemy import create_engine

# Load environment variables
load_dotenv()

def create_database():
    """Create the PostgreSQL database if it doesn't exist."""
    
    # Get database configuration from environment
    host = os.getenv("POSTGRES_HOST", "localhost")
    port = os.getenv("POSTGRES_PORT", "5432")
    user = os.getenv("POSTGRES_USER", "username")
    password = os.getenv("POSTGRES_PASSWORD", "password")
    database = os.getenv("POSTGRES_DB", "email_monitor")
    
    print(f"🔧 Connecting to PostgreSQL server at {host}:{port}")
    
    try:
        # Connect to PostgreSQL server (not to a specific database)
        conn = psycopg2.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database="postgres"  # Connect to default postgres database
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Check if database exists
        cursor.execute(
            "SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s",
            (database,)
        )
        exists = cursor.fetchone()
        
        if not exists:
            print(f"📦 Creating database '{database}'...")
            cursor.execute(f'CREATE DATABASE "{database}"')
            print(f"✅ Database '{database}' created successfully!")
        else:
            print(f"✅ Database '{database}' already exists.")
        
        cursor.close()
        conn.close()
        
    except psycopg2.Error as e:
        print(f"❌ Error connecting to PostgreSQL: {e}")
        return False
    
    return True

def create_tables():
    """Create the database tables using SQLAlchemy."""
    
    print("🔧 Creating database tables...")
    
    try:
        # Get database URL
        database_url = os.getenv(
            "DATABASE_URL",
            f"postgresql://{os.getenv('POSTGRES_USER', 'username')}:"
            f"{os.getenv('POSTGRES_PASSWORD', 'password')}@"
            f"{os.getenv('POSTGRES_HOST', 'localhost')}:"
            f"{os.getenv('POSTGRES_PORT', '5432')}/"
            f"{os.getenv('POSTGRES_DB', 'email_monitor')}"
        )
        
        # Create engine
        engine = create_engine(database_url)
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        
        print("✅ Database tables created successfully!")
        
        # List created tables
        from sqlalchemy import inspect
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        print("📋 Created tables:")
        for table in tables:
            print(f"   - {table}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        return False

def test_connection():
    """Test the database connection."""
    
    print("🔍 Testing database connection...")
    
    try:
        # Test connection using our get_db function
        db = get_db()
        
        # Try a simple query
        from app.models import EmailLog
        count = db.query(EmailLog).count()
        
        print(f"✅ Database connection successful! Found {count} emails in database.")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection test failed: {e}")
        return False

def main():
    """Main initialization function."""
    
    print("🚀 Email Monitor Agent - PostgreSQL Database Initialization")
    print("=" * 60)
    
    # Step 1: Create database
    if not create_database():
        print("❌ Failed to create database. Exiting.")
        sys.exit(1)
    
    # Step 2: Create tables
    if not create_tables():
        print("❌ Failed to create tables. Exiting.")
        sys.exit(1)
    
    # Step 3: Test connection
    if not test_connection():
        print("❌ Database connection test failed. Exiting.")
        sys.exit(1)
    
    print("=" * 60)
    print("🎉 PostgreSQL database initialization completed successfully!")
    print("")
    print("📋 Next steps:")
    print("1. Update your .env file with the correct PostgreSQL credentials")
    print("2. Start the Email Monitor Agent with: python run.py")
    print("")
    print("🔧 Database Configuration:")
    print(f"   Host: {os.getenv('POSTGRES_HOST', 'localhost')}")
    print(f"   Port: {os.getenv('POSTGRES_PORT', '5432')}")
    print(f"   Database: {os.getenv('POSTGRES_DB', 'email_monitor')}")
    print(f"   User: {os.getenv('POSTGRES_USER', 'username')}")

if __name__ == "__main__":
    main()
